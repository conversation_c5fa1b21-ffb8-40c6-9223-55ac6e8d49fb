# 任务调度系统

## 概述

本系统提供了完整的任务调度功能，支持多种调度策略、资源管理、依赖关系设置、失败处理和负载均衡等特性。

## 功能特性

### 调度策略配置
- **定时执行**: 支持Cron表达式定时执行
- **周期执行**: 支持按固定间隔周期执行
- **触发执行**: 支持事件触发执行
- **条件执行**: 支持条件满足时执行

### 执行时间设置
- **Cron表达式**: 灵活的时间调度配置
- **可视化时间选择**: 支持开始时间和结束时间设置
- **时区设置**: 支持不同时区的时间调度
- **节假日处理**: 支持跳过、执行或延迟节假日任务

### 资源分配管理
- **CPU限制**: 设置任务执行的CPU核数限制
- **内存限制**: 设置任务执行的内存使用限制
- **并发数控制**: 控制同时执行的任务数量
- **队列优先级**: 设置任务在队列中的优先级

### 依赖关系设置
- **前置任务**: 设置任务执行前必须完成的任务
- **后置任务**: 设置任务完成后需要执行的任务
- **条件依赖**: 基于条件表达式的依赖关系
- **结果传递**: 支持任务间的数据传递

### 失败处理策略
- **重试次数**: 设置任务失败后的重试次数
- **重试间隔**: 设置重试之间的时间间隔
- **失败通知**: 配置任务失败时的通知方式
- **降级处理**: 设置任务失败后的降级策略

### 负载均衡
- **任务分发**: 支持多种负载均衡算法
- **资源监控**: 监控执行节点的资源使用情况
- **动态调整**: 根据负载情况动态调整任务分配
- **性能优化**: 优化任务执行性能

## 数据库表结构

### 1. 任务表 (task)
存储系统预定义的任务类型，包含以下字段：
- `task_code`: 任务代码（唯一）
- `task_name`: 任务名称
- `task_description`: 任务描述
- `task_type`: 任务类型（SYSTEM/CUSTOM）
- `category`: 任务分类
- `default_config`: 默认配置参数（JSON）
- `status`: 状态（0:禁用，1:启用）

### 2. 调度表 (schedule)
存储用户创建的调度配置，包含完整的调度策略、资源配置等信息。

### 3. 调度任务链表 (schedule_task)
存储调度中包含的任务链，支持任务间的依赖关系和结果传递。

## API接口

### 调度管理
- `POST /api/schedule/create` - 创建调度
- `PUT /api/schedule/update` - 更新调度
- `DELETE /api/schedule/{id}` - 删除调度
- `GET /api/schedule/{id}` - 获取调度详情
- `GET /api/schedule/list` - 获取用户所有调度
- `PUT /api/schedule/{id}/status` - 更新调度状态

### 任务管理
- `GET /api/schedule/tasks` - 获取所有可用任务

## 使用示例

### 创建调度请求示例
```json
{
  "scheduleName": "数据同步任务",
  "description": "每日数据同步调度",
  "scheduleType": "CRON",
  "cronExpression": "0 0 2 * * ?",
  "timezone": "Asia/Shanghai",
  "holidayHandle": "SKIP",
  "cpuLimit": 2.0,
  "memoryLimit": 1024,
  "maxConcurrency": 1,
  "queuePriority": 5,
  "maxRetryCount": 3,
  "retryInterval": 60,
  "failureNotify": "{\"email\":\"<EMAIL>\"}",
  "fallbackAction": "STOP",
  "loadBalanceType": "ROUND_ROBIN",
  "tasks": [
    {
      "taskId": 1,
      "taskOrder": 1,
      "dependencyType": "SUCCESS",
      "taskConfig": "{}",
      "timeoutSeconds": 3600
    }
  ]
}
```

## 部署说明

1. 执行数据库DDL语句创建相关表结构
2. 系统会自动插入默认的任务数据
3. 启动服务后即可通过API接口进行调度管理

## 默认任务类型

系统预置了以下任务类型：
- 数据同步 (DATA_SYNC)
- 文件处理 (FILE_PROCESS)
- 邮件发送 (EMAIL_SEND)
- 报表生成 (REPORT_GENERATE)
- 数据备份 (DATA_BACKUP)
- 系统监控 (SYSTEM_MONITOR)
- 日志清理 (LOG_CLEANUP)
- API调用 (API_CALL)
- 数据验证 (DATA_VALIDATION)
- 缓存刷新 (CACHE_REFRESH)

## 注意事项

1. 本系统只负责调度配置的存储和管理，不包含实际的任务执行引擎
2. 所有接口都需要用户登录认证
3. 调度配置支持JSON格式的扩展配置
4. 建议根据实际业务需求调整资源限制和重试策略
