package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"io"
)

// GenerateKeyFromUsername 从用户名生成32字节密钥(AES-256)
func GenerateKeyFromUsername(username string) []byte {
	hash := sha256.Sum256([]byte(username))
	return hash[:]
}

func encrypt(key []byte, plaintext []byte) (keyB64, nonceB64, ciphertextB64 string, err error) {
	// 生成随机AES-256密钥
	//key := make([]byte, 32)
	//if _, err := rand.Read(key); err != nil {
	//	return "", "", "", err
	//}

	// 创建AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", "", "", err
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", "", "", err
	}

	// 生成随机Nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", "", "", err
	}

	// 加密数据
	ciphertext := gcm.Seal(nil, nonce, plaintext, nil)

	// Base64编码
	keyB64 = base64.StdEncoding.EncodeToString(key)
	nonceB64 = base64.StdEncoding.EncodeToString(nonce)
	ciphertextB64 = base64.StdEncoding.EncodeToString(ciphertext)
	return keyB64, nonceB64, ciphertextB64, nil
}

func decrypt(keyB64, nonceB64, ciphertextB64 string) ([]byte, error) {
	// Base64解码
	key, err := base64.StdEncoding.DecodeString(keyB64)
	if err != nil {
		return nil, err
	}
	nonce, err := base64.StdEncoding.DecodeString(nonceB64)
	if err != nil {
		return nil, err
	}
	ciphertext, err := base64.StdEncoding.DecodeString(ciphertextB64)
	if err != nil {
		return nil, err
	}

	// 创建AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	// 解密数据
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, errors.New("解密失败，数据可能被篡改")
	}
	return plaintext, nil
}
