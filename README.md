# Mozhao

一个基于Go的Web应用程序，支持AI集成、数据采集和多环境配置。

## 快速开始

### 编译
```bash
go build -o mozhao.exe src/main.go
```

### 运行
```bash
# 使用嵌入的默认配置
./mozhao.exe

# 使用外部配置文件（放在可执行文件同级目录）
# 创建 application.yml 配置文件，然后运行
./mozhao.exe
```

### 环境配置
```bash
# 开发环境
set GO_ENV=dev
./mozhao.exe

# 生产环境
set GO_ENV=prod
./mozhao.exe
```

## 配置说明

程序支持多层配置，优先级从低到高：

1. **嵌入的默认配置** - 编译时嵌入的配置
2. **嵌入的环境配置** - 根据 GO_ENV 加载的环境配置
3. **外部配置文件** - 可执行文件同级目录的 `application.yml`
4. **外部环境配置** - 可执行文件同级目录的 `application-{env}.yml`
5. **环境变量** - `MOZHAO_*` 格式的环境变量

## 部署

### 单文件部署
```bash
# 只需要一个可执行文件，使用嵌入的配置
./mozhao.exe
```

### 自定义配置部署
```bash
# 将配置文件与可执行文件放在同一目录
./mozhao.exe
./application.yml
```

## 项目结构

```
src/
├── ai/                 # AI模块
├── common/            # 公共模块
│   ├── mysql/         # 数据库
│   ├── rdb/           # Redis
│   └── utils/         # 工具类
├── config/            # 配置模块（包含嵌入的配置文件）
├── controller/        # 控制器
├── model/            # 数据模型
├── scheduler/        # 调度器
├── service/          # 业务逻辑
├── start/            # 启动模块
└── main.go           # 主程序入口
```

#### 软件架构
软件架构说明


#### 安装教程

1.  xxxx
2.  xxxx
3.  xxxx

#### 使用说明

1.  xxxx
2.  xxxx
3.  xxxx

#### 参与贡献

1.  Fork 本仓库
2.  新建 Feat_xxx 分支
3.  提交代码
4.  新建 Pull Request


#### 特技

1.  使用 Readme\_XXX.md 来支持不同的语言，例如 Readme\_en.md, Readme\_zh.md
2.  Gitee 官方博客 [blog.gitee.com](https://blog.gitee.com)
3.  你可以 [https://gitee.com/explore](https://gitee.com/explore) 这个地址来了解 Gitee 上的优秀开源项目
4.  [GVP](https://gitee.com/gvp) 全称是 Gitee 最有价值开源项目，是综合评定出的优秀开源项目
5.  Gitee 官方提供的使用手册 [https://gitee.com/help](https://gitee.com/help)
6.  Gitee 封面人物是一档用来展示 Gitee 会员风采的栏目 [https://gitee.com/gitee-stars/](https://gitee.com/gitee-stars/)
