package cad

import (
	"errors"
	"mozhao/src/common/consts"
	"mozhao/src/common/mysql"
	"mozhao/src/common/utils"
	"mozhao/src/model/entity"

	"github.com/gin-gonic/gin"
)

type addCadModel struct {
	Cads []cadModel `json:"cads" from:"cads"`
}

type cadModel struct {
	URL string  `json:"url" example:"https://example.com/image.jpg"`
	X   float64 `json:"x" example:"100.5"`
	Y   float64 `json:"y" example:"200.3"`
}

func Add(context *gin.Context) (any, error) {
	var cadModels addCadModel
	err := context.ShouldBind(&cadModels)
	if err != nil {
		return false, errors.New(consts.ParamError)
	}
	cadId := utils.GenerateSnowFlakeID()
	for _, cad := range cadModels.Cads {
		cadEntity := entity.CadEntity{
			URL:   cad.URL,
			X:     cad.X,
			Y:     cad.Y,
			CadID: cadId,
		}
		mysql.Db.Create(&cadEntity)
	}
	return true, nil
}

type queryCadModel struct {
	CadID int64 `json:"cadId" form:"cadId"`
}

func Query(context *gin.Context) (any, error) {
	var result []entity.CadEntity
	mysql.Db.Find(&result)
	return result, nil
}
