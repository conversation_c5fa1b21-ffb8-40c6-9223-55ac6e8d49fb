# Technology Stack

## Core Technologies

- **Language**: Go 1.24
- **Web Framework**: Gin (HTTP router and middleware)
- **Database**: MySQL with GORM ORM
- **Cache**: Redis
- **Authentication**: JWT tokens with RSA encryption
- **Documentation**: Swagger/OpenAPI with gin-swagger
- **WebSocket**: Gorilla WebSocket
- **Configuration**: Viper (YAML-based config)

## Key Dependencies

- **AI Integration**: CloudWeGo Eino with OpenAI extensions
- **Security**: golang.org/x/crypto for encryption
- **Database**: gorm.io/gorm + gorm.io/driver/mysql
- **Utilities**: 
  - github.com/bwmarrin/snowflake (ID generation)
  - github.com/jinzhu/copier (struct copying)
  - github.com/spf13/viper (configuration)

## Build Commands

### Development Build
```bash
go build -o mozhao.exe src/main.go
```

### Linux Production Build
```bash
# Windows PowerShell
$env:GOOS="linux"; $env:GOARCH="amd64"; $env:CGO_ENABLED="0"; go build -o mozhao-server-linux-amd64 src/main.go

# Windows CMD
set GOOS=linux && set GOARCH=amd64 && set CGO_ENABLED=0 && go build -o mozhao-server-linux-amd64 src/main.go
```

### Documentation Generation
```bash
# Install swag tool
go install github.com/swaggo/swag/cmd/swag@latest

# Generate Swagger docs
swag init -g src/start/web_start.go -o docs
```

## Deployment

### Local Development
```bash
# Set environment
set GO_ENV=dev
./mozhao.exe
```

### Linux Production
```bash
chmod +x mozhao-server-linux-amd64
nohup ./mozhao-server-linux-amd64 > ./app.log 2>&1 &
```

## Configuration

Multi-layer configuration system with priority order:
1. Embedded default config
2. Embedded environment config (based on GO_ENV)
3. External `application.yml`
4. External `application-{env}.yml`
5. Environment variables (`MOZHAO_*` prefix)