package controller

import (
	"mozhao/src/service"
	ipService "mozhao/src/service/ip"

	"github.com/gin-gonic/gin"
)

func IpController(rootGroup *gin.RouterGroup) {
	ipGroup := rootGroup.Group("/ip", service.PrivilegeCheck)
	ipGroup.POST("/getIp", getIp)
}

// getIp 获取IP信息
// @Summary	查询IP代理信息
// @Description 根据地址查询可用的IP代理信息
// @Tags	ip
// @Accept	json
// @Produce	json
// @Param	body body ip.address true "地址信息"
// @Success	200 {object} utils.Result{data=ipdata.IpData} "IP代理信息"
// @Router	/api/ip/getIp [post]
func getIp(context *gin.Context) {
	Handler(context, ipService.GetIp)
}
