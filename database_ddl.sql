-- 数据采集系统数据库DDL语句
-- 创建时间: 2025-01-08
-- 说明: 包含5个数据采集表的完整DDL定义

-- 1. 用户资料数据表
CREATE TABLE `profile_data` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `profile_data_id` varchar(255) DEFAULT NULL COMMENT '原始档案ID',
  `username` varchar(255) DEFAULT NULL COMMENT '用户名',
  `display_name` varchar(255) DEFAULT NULL COMMENT '显示名称',
  `bio` text COMMENT '个人简介',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `follower_count` bigint DEFAULT '0' COMMENT '粉丝数',
  `following_count` bigint DEFAULT '0' COMMENT '关注数',
  `post_count` bigint DEFAULT '0' COMMENT '帖子数',
  `verified` tinyint(1) DEFAULT '0' COMMENT '是否认证',
  `business_account` tinyint(1) DEFAULT '0' COMMENT '是否商业账户',
  `email` varchar(255) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(50) DEFAULT NULL COMMENT '电话',
  `website` varchar(500) DEFAULT NULL COMMENT '网站',
  `country` varchar(100) DEFAULT NULL COMMENT '国家',
  `city` varchar(100) DEFAULT NULL COMMENT '城市',
  `workplace` varchar(255) DEFAULT NULL COMMENT '工作地点',
  `position` varchar(255) DEFAULT NULL COMMENT '职位',
  `education` varchar(255) DEFAULT NULL COMMENT '教育背景',
  `profile_url` varchar(500) DEFAULT NULL COMMENT '档案URL',
  `platform` varchar(50) DEFAULT NULL COMMENT '平台',
  `collected_at` datetime DEFAULT NULL COMMENT '采集时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_platform` (`platform`),
  KEY `idx_collected_at` (`collected_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户资料数据表';

-- 2. 帖子内容数据表
CREATE TABLE `post_data` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `post_data_id` varchar(255) DEFAULT NULL COMMENT '原始帖子ID',
  `author_id` varchar(255) DEFAULT NULL COMMENT '作者ID',
  `author_name` varchar(255) DEFAULT NULL COMMENT '作者名称',
  `content` longtext COMMENT '内容',
  `media_urls` json DEFAULT NULL COMMENT '媒体URL列表',
  `media_types` json DEFAULT NULL COMMENT '媒体类型列表',
  `like_count` bigint DEFAULT '0' COMMENT '点赞数',
  `comment_count` bigint DEFAULT '0' COMMENT '评论数',
  `share_count` bigint DEFAULT '0' COMMENT '分享数',
  `view_count` bigint DEFAULT '0' COMMENT '浏览数',
  `published_at` datetime DEFAULT NULL COMMENT '发布时间',
  `timestamp` varchar(100) DEFAULT NULL COMMENT '时间戳',
  `hashtags` json DEFAULT NULL COMMENT '话题标签',
  `mentions` json DEFAULT NULL COMMENT '提及用户',
  `post_url` varchar(500) DEFAULT NULL COMMENT '帖子URL',
  `platform` varchar(50) DEFAULT NULL COMMENT '平台',
  `collected_at` datetime DEFAULT NULL COMMENT '采集时间',
  `is_ad` tinyint(1) DEFAULT '0' COMMENT '是否广告',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_platform` (`platform`),
  KEY `idx_published_at` (`published_at`),
  KEY `idx_collected_at` (`collected_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帖子内容数据表';

-- 3. 联系人数据表
CREATE TABLE `contact_data` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `contact_data_id` varchar(255) DEFAULT NULL COMMENT '原始联系人ID',
  `name` varchar(255) DEFAULT NULL COMMENT '姓名',
  `username` varchar(255) DEFAULT NULL COMMENT '用户名',
  `email` varchar(255) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(50) DEFAULT NULL COMMENT '电话',
  `company` varchar(255) DEFAULT NULL COMMENT '公司',
  `position` varchar(255) DEFAULT NULL COMMENT '职位',
  `facebook` varchar(500) DEFAULT NULL COMMENT 'Facebook',
  `instagram` varchar(500) DEFAULT NULL COMMENT 'Instagram',
  `twitter` varchar(500) DEFAULT NULL COMMENT 'Twitter',
  `linkedin` varchar(500) DEFAULT NULL COMMENT 'LinkedIn',
  `tags` json DEFAULT NULL COMMENT '标签',
  `source` varchar(255) DEFAULT NULL COMMENT '来源',
  `notes` text COMMENT '备注',
  `collected_at` datetime DEFAULT NULL COMMENT '采集时间',
  `last_contact` datetime DEFAULT NULL COMMENT '最后联系时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_name` (`name`),
  KEY `idx_company` (`company`),
  KEY `idx_source` (`source`),
  KEY `idx_collected_at` (`collected_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='联系人数据表';

-- 4. 搜索结果数据表
CREATE TABLE `search_result_data` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `search_result_data_id` varchar(255) DEFAULT NULL COMMENT '原始搜索结果ID',
  `name` varchar(255) DEFAULT NULL COMMENT '名称',
  `username` varchar(255) DEFAULT NULL COMMENT '用户名',
  `profile_url` varchar(500) DEFAULT NULL COMMENT '档案URL',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `description` text COMMENT '描述',
  `snippet` text COMMENT '摘要',
  `follower_count` bigint DEFAULT '0' COMMENT '粉丝数',
  `keyword` varchar(255) DEFAULT NULL COMMENT '搜索关键词',
  `platform` varchar(50) DEFAULT NULL COMMENT '平台',
  `rank` int DEFAULT NULL COMMENT '排名',
  `collected_at` datetime DEFAULT NULL COMMENT '采集时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_keyword` (`keyword`),
  KEY `idx_platform` (`platform`),
  KEY `idx_rank` (`rank`),
  KEY `idx_collected_at` (`collected_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索结果数据表';

-- 5. 群组社区数据表
CREATE TABLE `group_data` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `group_data_id` varchar(255) DEFAULT NULL COMMENT '原始群组ID',
  `name` varchar(255) DEFAULT NULL COMMENT '群组名称',
  `description` text COMMENT '群组描述',
  `member_count` bigint DEFAULT '0' COMMENT '成员数',
  `is_private` tinyint(1) DEFAULT '0' COMMENT '是否私有',
  `category` varchar(100) DEFAULT NULL COMMENT '分类',
  `admin_ids` json DEFAULT NULL COMMENT '管理员ID列表',
  `rules` json DEFAULT NULL COMMENT '群组规则',
  `group_url` varchar(500) DEFAULT NULL COMMENT '群组URL',
  `platform` varchar(50) DEFAULT NULL COMMENT '平台',
  `collected_at` datetime DEFAULT NULL COMMENT '采集时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_name` (`name`),
  KEY `idx_platform` (`platform`),
  KEY `idx_category` (`category`),
  KEY `idx_collected_at` (`collected_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='群组社区数据表';

-- 添加外键约束 (可选，根据实际需求决定是否启用)
-- ALTER TABLE `profile_data` ADD CONSTRAINT `fk_profile_data_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `post_data` ADD CONSTRAINT `fk_post_data_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `contact_data` ADD CONSTRAINT `fk_contact_data_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `search_result_data` ADD CONSTRAINT `fk_search_result_data_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `group_data` ADD CONSTRAINT `fk_group_data_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE;