package entity

type UserPlatformEntity struct {
	BaseEntity
	PlatformUsername string `json:"platformUsername" binding:"required"`
	PlatformPassword string `json:"platformPassword" binding:"required"`
	PlatformCode     string `json:"platformCode"`
	UserId           int64  `json:"userId"`
}

func (UserPlatformEntity) TableName() string {
	return "user_platform"
}

func (platform UserPlatformEntity) PlatformEncode() {

}

func (platform UserPlatformEntity) PlatformDecode() {

}
