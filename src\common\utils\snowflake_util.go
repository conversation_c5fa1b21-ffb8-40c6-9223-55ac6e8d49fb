package utils

import (
	"errors"
	"sync"
	"time"

	"github.com/bwmarrin/snowflake"
)

// 全局配置
var (
	node        *snowflake.Node
	nodeMutex   sync.Mutex
	initialized bool
	// 默认起始时间(2024-01-01 00:00:00 UTC)
	defaultEpoch = time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC).UnixNano() / 1e6
)

// init 初始化雪花算法节点（程序启动时调用）
func init() {
	// 默认使用机器ID为1
	err := initSnowFlake(1)
	if err != nil {
		panic("Failed to initialize snowflake: " + err.Error())
	}
}

// initSnowFlake 初始化雪花算法节点的内部实现
func initSnowFlake(machineID int64) error {
	nodeMutex.Lock()
	defer nodeMutex.Unlock()

	if initialized {
		return errors.New("snowflake already initialized")
	}

	// 校验机器ID范围
	if machineID < 0 || machineID > 1023 {
		return errors.New("machine ID must be between 0 and 1023")
	}

	// 检查系统时间是否早于起始时间
	current := time.Now().UnixMilli()
	if current < defaultEpoch {
		return errors.New("system time is earlier than epoch time")
	}

	// 创建新节点
	snowflake.Epoch = int64(uint64(defaultEpoch))
	n, err := snowflake.NewNode(machineID)
	if err != nil {
		return err
	}

	node = n
	initialized = true
	return nil
}

// GenerateSnowFlakeID 生成分布式ID
func GenerateSnowFlakeID() int64 {
	return node.Generate().Int64()
}

// ParseID 解析ID的组成成分
func ParseID(id int64) map[string]interface{} {
	return map[string]interface{}{
		"timestamp": id>>22 + defaultEpoch, // 时间戳
		"machine":   (id >> 12) & 0x3FF,    // 机器ID
		"sequence":  id & 0xFFF,            // 序列号
	}
}
