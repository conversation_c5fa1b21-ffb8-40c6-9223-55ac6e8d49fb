package ai

type AiMessage struct {
	Model            string         `json:"model"`
	Messages         []Message      `json:"messages"`
	Stream           bool           `json:"stream"`
	MaxToken         string         `json:"max_token"`
	Stop             any            `json:"stop"`
	Temperature      float64        `json:"temperature"`
	TopP             float64        `json:"top_p"`
	TopK             float64        `json:"top_k"`
	FrequencyPenalty float64        `json:"frequency_penalty"`
	N                int            `json:"n"`
	ResponseFormat   ResponseFormat `json:"response_format"`
	Tools            []Tool         `json:"tools"`
}
type ResponseFormat struct {
	Type string `json:"type"`
}

type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type Tool struct {
	Type     string   `json:"type"`
	Function Function `json:"function"`
}

type Function struct {
	Description string     `json:"description"`
	Name        string     `json:"name"`
	Parameters  []struct{} `json:"parameters"`
	Strict      bool       `json:"strict"`
}
