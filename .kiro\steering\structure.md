# Project Structure

## Directory Organization

```
src/
├── ai/                 # AI integration modules
├── common/            # Shared utilities and components
│   ├── consts/        # Application constants
│   ├── ipdata/        # IP data handling
│   ├── mysql/         # Database connection and utilities
│   ├── rdb/           # Redis connection and utilities
│   └── utils/         # General utility functions
├── config/            # Configuration management
├── controller/        # HTTP request handlers (Gin controllers)
├── model/             # Data models and structures
│   ├── ai/            # AI-related models
│   ├── entity/        # Database entities
│   └── vo/            # Value objects (DTOs)
├── service/           # Business logic layer
│   ├── ai/            # AI service implementations
│   ├── cad/           # CAD data services
│   ├── gather/        # Data collection services
│   ├── ip/            # IP proxy services
│   ├── job/           # Job management services
│   ├── schedule/      # Task scheduling services
│   ├── secret/        # Security/encryption services
│   ├── user/          # User management services
│   └── ws/            # WebSocket services
├── start/             # Application startup logic
├── timer/             # Timer and scheduled task management
└── main.go            # Application entry point
```

## Architecture Patterns

### Layered Architecture
- **Controller Layer**: HTTP request handling and routing
- **Service Layer**: Business logic implementation
- **Model Layer**: Data structures and database entities
- **Common Layer**: Shared utilities and infrastructure

### Configuration Structure
- Embedded YAML configs in `src/config/`
- Environment-specific configs: `application-{env}.yml`
- External config override support

### API Structure
- Base path: `/api`
- Module-based routing: `/api/{module}/{action}`
- Swagger documentation at `/api/swagger/index.html`

## Naming Conventions

### Files and Directories
- Snake_case for directories: `src/common/mysql/`
- Snake_case for Go files: `user_controller.go`
- Descriptive module names: `ai_controller.go`, `base_service.go`

### Go Code Conventions
- PascalCase for exported functions and types
- camelCase for unexported functions and variables
- Interface suffix: `Service`, `Repository`
- Controller suffix: `Controller`
- Model suffix: `Entity`, `VO` (Value Object)

### API Endpoints
- RESTful patterns where applicable
- Module-based grouping: `/api/user/login`, `/api/ai/textMessage`
- Action-oriented naming for complex operations

## Key Files

- `src/main.go`: Application entry point
- `src/start/web_start.go`: Web server initialization and routing
- `src/config/config.go`: Configuration structure definitions
- `src/service/base_service.go`: Common service utilities (auth, validation)
- `docs/`: Auto-generated Swagger documentation