package ip

import (
	"errors"
	"math/rand"
	"mozhao/src/common/consts"
	"mozhao/src/common/ipdata"

	"github.com/gin-gonic/gin"
)

type address struct {
	Address string `json:"address" binding:"required" example:"北京"`
}

func GetIp(context *gin.Context) (any, error) {
	var address address
	context.ShouldBind(&address)
	data := ipdata.IpDataMap[address.Address]
	if data == nil || len(data) == 0 {
		return false, errors.New(consts.NotExistError)
	}
	randomIndex := rand.Intn(len(data))
	return data[randomIndex], nil
}
