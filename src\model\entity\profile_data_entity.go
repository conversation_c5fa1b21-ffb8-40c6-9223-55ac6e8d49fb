package entity

import "time"

// ProfileDataEntity 用户资料数据实体
type ProfileDataEntity struct {
	BaseEntity
	UserId          int64     `gorm:"column:user_id;type:bigint;not null;index;comment:用户ID" json:"userId"`
	ProfileDataID   string    `gorm:"column:profile_data_id;type:varchar(255);comment:原始档案ID" json:"profileDataId"`
	Username        string    `gorm:"column:username;type:varchar(255);comment:用户名" json:"username"`
	DisplayName     string    `gorm:"column:display_name;type:varchar(255);comment:显示名称" json:"displayName"`
	Bio             string    `gorm:"column:bio;type:text;comment:个人简介" json:"bio,omitempty"`
	Avatar          string    `gorm:"column:avatar;type:varchar(500);comment:头像URL" json:"avatar,omitempty"`
	FollowerCount   int64     `gorm:"column:follower_count;type:bigint;default:0;comment:粉丝数" json:"followerCount,omitempty"`
	FollowingCount  int64     `gorm:"column:following_count;type:bigint;default:0;comment:关注数" json:"followingCount,omitempty"`
	PostCount       int64     `gorm:"column:post_count;type:bigint;default:0;comment:帖子数" json:"postCount,omitempty"`
	Verified        bool      `gorm:"column:verified;type:boolean;default:false;comment:是否认证" json:"verified,omitempty"`
	BusinessAccount bool      `gorm:"column:business_account;type:boolean;default:false;comment:是否商业账户" json:"businessAccount,omitempty"`
	Email           string    `gorm:"column:email;type:varchar(255);comment:邮箱" json:"email,omitempty"`
	Phone           string    `gorm:"column:phone;type:varchar(50);comment:电话" json:"phone,omitempty"`
	Website         string    `gorm:"column:website;type:varchar(500);comment:网站" json:"website,omitempty"`
	Country         string    `gorm:"column:country;type:varchar(100);comment:国家" json:"country,omitempty"`
	City            string    `gorm:"column:city;type:varchar(100);comment:城市" json:"city,omitempty"`
	Workplace       string    `gorm:"column:workplace;type:varchar(255);comment:工作地点" json:"workplace,omitempty"`
	Position        string    `gorm:"column:position;type:varchar(255);comment:职位" json:"position,omitempty"`
	Education       string    `gorm:"column:education;type:varchar(255);comment:教育背景" json:"education,omitempty"`
	ProfileUrl      string    `gorm:"column:profile_url;type:varchar(500);comment:档案URL" json:"profileUrl"`
	Platform        string    `gorm:"column:platform;type:varchar(50);comment:平台" json:"platform"`
	CollectedAt     time.Time `gorm:"column:collected_at;type:datetime;comment:采集时间" json:"collectedAt"`
}

// TableName 指定表名
func (ProfileDataEntity) TableName() string {
	return "profile_data"
}
