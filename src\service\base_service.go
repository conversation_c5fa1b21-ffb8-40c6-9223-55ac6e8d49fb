package service

import (
	"errors"
	"fmt"
	"mozhao/src/common/consts"
	"mozhao/src/common/utils"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

var re = regexp.MustCompile(`/(\w+)/(\w+)`)

func LoginCheck(context *gin.Context) {
	token := context.GetHeader(consts.SecretHttpHead)
	loginCheckHandler(token, context)
}

func loginCheckHandler(token string, context *gin.Context) {
	userId, err := GetLoginUserId(token)
	if err != nil {
		utils.Err(err.Error(), context)
		return
	}
	context.Set("userId", userId)
}

func PrivilegeCheck(context *gin.Context) {
	token := context.GetHeader(consts.SecretHttpHead)
	privilegeCheckHandler(token, context)
}

func privilegeCheckHandler(token string, context *gin.Context) {
	userId, err := GetLoginUserId(token)
	if err != nil {
		utils.Err(err.Error(), context)
		return
	}
	context.Set("userId", userId)
}

func FullCheck(context *gin.Context) {
	token := context.GetHeader(consts.SecretHttpHead)
	fullCheckHandler(token, context)
}

func fullCheckHandler(token string, context *gin.Context) {
	userId, err := GetLoginUserId(token)
	if err != nil {
		utils.Err(err.Error(), context)
		return
	}
	context.Set("userId", userId)
}

func GetLoginUserId(token string) (int64, error) {
	if token == "" {
		return 0, errors.New(consts.TokenError)
	}
	claims, err := utils.ParseToken(token)
	if err != nil {
		return 0, errors.New(consts.TokenError)
	}
	exp := claims["exp"].(float64)
	if time.Now().Unix() > int64(exp) {
		return 0, errors.New(consts.TokenExpired)
	}
	userId := claims["userId"].(float64)
	return int64(userId), nil
}

func GetApiPath(context *gin.Context) string {
	path := context.Request.URL.Path
	matches := re.FindStringSubmatch(path)
	if len(matches) >= 3 {
		return fmt.Sprintf("/%s/%s", matches[1], matches[2])
	}
	return ""
}

func GetApiName(context *gin.Context) string {
	path := context.Request.URL.Path
	parts := strings.Split(path, "/")
	if len(parts) >= 3 {
		return parts[len(parts)-1]
	}
	return ""
}
