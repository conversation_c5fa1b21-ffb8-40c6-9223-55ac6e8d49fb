package ai

import (
	"github.com/cloudwego/eino/schema"
)

func CreateImageMessages(content string) []*schema.Message {
	messages := []*schema.Message{
		// 系统消息
		schema.SystemMessage("你是一个助手"),
		// 多模态消息（包含图片）
		{
			Role: schema.User,
			MultiContent: []schema.ChatMessagePart{
				{
					Type: schema.ChatMessagePartTypeImageURL,
					ImageURL: &schema.ChatMessageImageURL{
						URL:    content,
						Detail: "high",
					},
				},
				{
					Type: schema.ChatMessagePartTypeText,
					Text: "这是一个验证码，请告诉我验证码的内容是什么？请只返回验证码文字，不要其他任何解释。",
				},
			},
		},
	}
	return messages
}

func CreateMessagesCaptchaChange(content string) []*schema.Message {
	messages := []*schema.Message{
		// 系统消息
		schema.SystemMessage("你是一个助手"),
		// 多模态消息（包含图片）
		{
			Role: schema.User,
			MultiContent: []schema.ChatMessagePart{
				{
					Type: schema.ChatMessagePartTypeText,
					Text: content,
				},
				{
					Type: schema.ChatMessagePartTypeText,
					Text: "转成6位的验证码 请只返回验证码文字，不要其他任何解释。",
				},
			},
		},
	}
	return messages
}

func CreateTextMessages(contents []string) []*schema.Message {
	messages := []*schema.Message{
		schema.SystemMessage("你是一个助手"),
		{
			Role:         schema.User,
			MultiContent: makeMultiContent(contents),
		},
	}
	return messages
}

func makeMultiContent(contents []string) []schema.ChatMessagePart {
	parts := make([]schema.ChatMessagePart, 0, len(contents)+1)

	// 添加动态文本内容
	for _, content := range contents {
		parts = append(parts, schema.ChatMessagePart{
			Type: schema.ChatMessagePartTypeText,
			Text: content,
		})
	}
	return parts
}
