package entity

type UserEntity struct {
	BaseEntity
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	Nickname string `json:"nickname"`
	HeadUrl  string `json:"headUrl"`
	GcmKey   string `json:"gcmKey"`
}

func (UserEntity) TableName() string {
	return "user"
}

//func (m *User) BeforeCreate(tx *gorm.DB) (err error) {
//	m.CreateTime = time.Now()
//	m.UpdateTime = m.CreateTime
//	return
//}
//
//// BeforeSave 钩子会在创建或更新记录前被触发
//func (m *User) BeforeSave(tx *gorm.DB) (err error) {
//	m.UpdateTime = time.Now()
//	return
//}
