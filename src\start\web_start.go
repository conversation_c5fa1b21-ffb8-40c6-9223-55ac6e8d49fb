package start

import (
	"mozhao/src/config"
	"mozhao/src/controller"
	"mozhao/src/service/ws"
	"time"

	_ "mozhao/docs" // 导入生成的 docs 包

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// @title           Mozhao Server API
// @version         1.0
// @description     Mozhao 服务端 API 文档
// @host            localhost:8080
// @BasePath        /api
func StartWeb() *gin.Engine {
	var webServer = gin.Default()
	webServer.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		ExposeHeaders:    []string{"*"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))
	configData := config.ConfigData
	rootGroup := webServer.Group(configData.Server.Path)

	// 添加 Swagger 文档路由
	rootGroup.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// 注册 WebSocket 路由
	ws.RegisterWSRouter(rootGroup, "/ws")
	controller.AiController(rootGroup)
	controller.CadController(rootGroup)
	controller.UserController(rootGroup)
	controller.IpController(rootGroup)
	controller.GatherController(rootGroup)
	controller.JobController(rootGroup)
	controller.ScheduleController(rootGroup)
	controller.DataCollectionController(rootGroup)
	return webServer
}
