# Mozhao Server 构建和部署命令

## 📦 打包 Linux 可执行文件

### Windows PowerShell
```powershell
$env:GOOS="linux"; $env:GOARCH="amd64"; $env:CGO_ENABLED="0"; go build -o mozhao-server-linux-amd64 src/main.go
```

### Windows CMD
```cmd
set GOOS=linux
set GOARCH=amd64
set CGO_ENABLED=0
go build -o mozhao-server-linux-amd64 src/main.go
```

## 🚀 Linux 服务器启动

```bash
# 设置执行权限
chmod +x mozhao-server-linux-amd64

# 后台启动，日志输出到 app.log
nohup ./mozhao-server-linux-amd64 > ./app.log 2>&1 &

# 查看日志
tail -f app.log

# 停止服务
pkill -f mozhao-server-linux-amd64
```

## 📚 Swagger 文档生成 (可选)
```bash
# 安装 swag 工具
go install github.com/swaggo/swag/cmd/swag@latest

# 生成文档
swag init -g src/start/web_start.go -o docs
```


