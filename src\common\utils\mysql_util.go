package utils

import (
	"math"

	"gorm.io/gorm"
)

func SqlWhere(db *gorm.DB, b bool, sql string, args ...interface{}) {
	if b {
		db.Where(sql, args...)
	}
}

// 分页响应结构体
type PageResult[T any] struct {
	Page      int   `json:"page" example:"1"`       // 当前页码
	PageSize  int   `json:"pageSize" example:"10"`  // 每页数量
	Total     int64 `json:"total" example:"100"`    // 总记录数
	TotalPage int   `json:"totalPage" example:"10"` // 总页数
	List      []T   `json:"list"`                   // 数据列表
}

// 分页参数
type PageParam struct {
	Page     int `form:"page" json:"page" example:"1"`          // 从请求参数绑定
	PageSize int `form:"pageSize" json:"pageSize" example:"10"` // 从请求参数绑定
}

func (p *PageParam) Standardize() {
	if p.Page < 1 {
		p.Page = 1
	}
	if p.PageSize <= 0 || p.PageSize > 100 {
		p.PageSize = 10 // 默认每页10条
	}
}

func Paginate[T any](db *gorm.DB, param PageParam) (*PageResult[T], error) {
	param.Standardize()
	var result PageResult[T]
	result.Page = param.Page
	result.PageSize = param.PageSize

	// 查询总数
	if err := db.Model(new(T)).Count(&result.Total).Error; err != nil {
		return nil, err
	}

	// 计算总页数
	result.TotalPage = int(math.Ceil(float64(result.Total) / float64(param.PageSize)))

	// 分页查询数据
	offset := (param.Page - 1) * param.PageSize
	if err := db.Offset(offset).Limit(param.PageSize).Find(&result.List).Error; err != nil {
		return nil, err
	}

	return &result, nil
}
