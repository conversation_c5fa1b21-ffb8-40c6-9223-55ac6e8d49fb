package job

import (
	"encoding/json"
	"mozhao/src/common/utils"
	"mozhao/src/service/ws"

	"github.com/gin-gonic/gin"
)

type BrowserSession struct {
	PlatformCode     string
	PlatformUserName string
	SnowflakeID      int64
}

type BrowserStartReq struct {
	BrowserId   int64  `json:"browserId" binding:"required"`
	BrowserArgs string `json:"browserArgs" binding:"required"`
	BrowserEnv  string `json:"browserEnv" binding:"required"`
}

type WebBrowserStartRequest struct {
	PlatformCode     string `json:"platformCode" binding:"required" example:"FB"`
	PlatformUserName string `json:"platformUserName" binding:"required" example:"testuser"`
}

func WebBrowserStart(ctx *gin.Context) (any, error) {
	userId := utils.GetUser(ctx)
	var data WebBrowserStartRequest
	ctx.ShouldBind(&data)
	BrowserStart(userId, data.PlatformCode, data.PlatformUserName)
	return true, nil
}

var (
	userBrowserMap = utils.CreateTypedSyncMap[int64, *[]BrowserSession]()
)

func BrowserStart(userId int64, platformCode string, platformUserName string) int64 {
	snowId := utils.GenerateSnowFlakeID()
	if sessions, exists := userBrowserMap.Load(userId); exists {
		for _, s := range *sessions {
			if s.PlatformCode == platformCode && s.PlatformUserName == platformUserName {
				return s.SnowflakeID
			}
		}
	}

	newSessions := &[]BrowserSession{{
		PlatformCode:     platformCode,
		PlatformUserName: platformUserName,
		SnowflakeID:      snowId,
	}}

	actual, _ := userBrowserMap.LoadOrStore(userId, newSessions)
	if actual != newSessions {
		*actual = append(*actual, BrowserSession{
			PlatformCode:     platformCode,
			PlatformUserName: platformUserName,
			SnowflakeID:      snowId,
		})
	}

	sendMessage(userId, browserStartMessage(snowId))
	return snowId
}

func sendMessage(userId int64, message string) {
	ws.SendMessage(userId, message)
}

func browserStartMessage(snowId int64) string {
	message := map[string]interface{}{
		"type": "browserStart",
		"data": map[string]interface{}{
			"snowId": snowId,
		},
	}
	jsonData, _ := json.Marshal(message)
	return string(jsonData)
}
