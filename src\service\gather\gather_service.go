package gather

import (
	"errors"
	"mozhao/src/common/consts"
	"mozhao/src/common/mysql"
	"mozhao/src/common/utils"
	"mozhao/src/model/entity"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

type AddGatherRequest struct {
	PlatformId   int64                    `json:"platformId" example:"123456"`               // 平台ID，允许NULL
	PlatformCode string                   `json:"platformCode" example:"FB"`                 // 平台编码，最大长度100
	GatherTime   time.Time                `json:"gatherTime" example:"2024-01-01T00:00:00Z"` // 采集时间，允许NULL
	GatherType   string                   `json:"gatherType" example:"用户信息"`                 // 采集类型，最大长度100
	KeyWord      string                   `json:"keyWord" example:"关键词"`
	Results      []AddGatherResultRequest `json:"results"`
}

type AddGatherResultRequest struct {
	Url             string `json:"url" example:"https://example.com/profile"`
	AccountId       string `json:"accountId" example:"user123"`
	AccountName     string `json:"accountName" example:"用户名"`
	AccountSynopsis string `json:"accountSynopsis" example:"用户简介"`
}

// GatherPageResult 采集任务分页结果
type GatherPageResult struct {
	Page      int                           `json:"page" example:"1"`       // 当前页码
	PageSize  int                           `json:"pageSize" example:"10"`  // 每页数量
	Total     int64                         `json:"total" example:"100"`    // 总记录数
	TotalPage int                           `json:"totalPage" example:"10"` // 总页数
	List      []entity.CustomerGatherEntity `json:"list"`                   // 数据列表
}

type QueryGatherRequest struct {
	utils.PageParam
	PlatformCode string `json:"platformCode" form:"platformCode"` // 平台编码，最大长度100
	GatherType   string `json:"gatherType" form:"gatherType"`     // 采集类型，最大长度100
	KeyWord      string `json:"keyWord" form:"keyWord"`
}

func AddGather(ctx *gin.Context) (any, error) {
	var gathers []AddGatherRequest
	if err := ctx.ShouldBind(&gathers); err != nil {
		return false, errors.New(consts.ParamError)
	}
	userId := utils.GetUser(ctx)
	sameId := utils.GenerateSnowFlakeID()
	for _, gather := range gathers {
		gatherId := addGather(userId, sameId, gather)
		for _, gatherResult := range gather.Results {
			addGatherResult(gatherResult, gatherId)
		}
	}
	return true, nil
}

func addGather(userId int64, sameId int64, gather AddGatherRequest) int64 {
	gatherId := utils.GenerateSnowFlakeID()
	gatherEntity := entity.CustomerGatherEntity{
		BaseEntity: entity.BaseEntity{
			Id: gatherId,
		},
		UserId:       userId,
		SameId:       sameId,
		PlatformId:   gather.PlatformId,
		PlatformCode: gather.PlatformCode,
		GatherTime:   gather.GatherTime,
		GatherType:   gather.GatherType,
		KeyWord:      gather.KeyWord,
	}
	mysql.Db.Create(&gatherEntity)
	return gatherId
}

func addGatherResult(gatherResult AddGatherResultRequest, gatherId int64) {
	gatherResultId := utils.GenerateSnowFlakeID()
	gatherResultEntity := entity.CustomerGatherResultEntity{
		BaseEntity: entity.BaseEntity{
			Id: gatherResultId,
		},
		GatherId:        gatherId,
		Url:             gatherResult.Url,
		AccountId:       gatherResult.AccountId,
		AccountName:     gatherResult.AccountName,
		AccountSynopsis: gatherResult.AccountSynopsis,
	}
	mysql.Db.Create(&gatherResultEntity)
}

func QueryGather(ctx *gin.Context) (any, error) {
	var queryGatherRequest QueryGatherRequest
	ctx.ShouldBindQuery(&queryGatherRequest)
	userId := utils.GetUser(ctx)
	query := mysql.Db.Model(&entity.CustomerGatherEntity{})
	query.Where("user_id=?", userId)
	utils.SqlWhere(query, queryGatherRequest.GatherType != "", "gather_type = ?", queryGatherRequest.GatherType)
	utils.SqlWhere(query, queryGatherRequest.KeyWord != "", "key_word = ?", queryGatherRequest.KeyWord)
	utils.SqlWhere(query, queryGatherRequest.PlatformCode != "", "platform_code = ?", queryGatherRequest.PlatformCode)
	result, err := utils.Paginate[entity.CustomerGatherEntity](query, queryGatherRequest.PageParam)
	if err != nil {
		return false, errors.New(consts.ParamError)
	}
	return result, nil
}

type QueryGatherInfoRequest struct {
	Id string `json:"id" form:"id"`
}

func QueryGatherInfo(ctx *gin.Context) (any, error) {
	var queryGatherInfoRequest QueryGatherInfoRequest
	ctx.ShouldBindQuery(&queryGatherInfoRequest)
	userId := utils.GetUser(ctx)
	id, err := strconv.ParseInt(queryGatherInfoRequest.Id, 10, 64)
	if err != nil {
		return false, errors.New(consts.ParamError)
	}
	var gatherEntity entity.CustomerGatherEntity
	mysql.Db.Where("id=? and user_id=?", id, userId).First(&gatherEntity)
	var gatherResultEntities []entity.CustomerGatherResultEntity
	mysql.Db.Where("gather_id=?", id).Find(&gatherResultEntities)
	return map[string]interface{}{
		"gather": gatherEntity,
		"result": gatherResultEntities,
	}, nil
}
