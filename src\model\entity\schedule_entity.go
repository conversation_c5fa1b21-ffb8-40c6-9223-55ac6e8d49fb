package entity

import "time"

// ScheduleEntity 调度表 - 存储用户创建的调度配置
type ScheduleEntity struct {
	BaseEntity
	UserId          int64     `json:"userId" gorm:"column:user_id;type:bigint;not null;comment:用户ID"`
	ScheduleName    string    `json:"scheduleName" gorm:"column:schedule_name;type:varchar(100);not null;comment:调度名称"`
	Description     string    `json:"description" gorm:"column:description;type:text;comment:调度描述"`
	
	// 调度策略配置
	ScheduleType    string    `json:"scheduleType" gorm:"column:schedule_type;type:varchar(20);not null;comment:调度类型(CRON,INTERVAL,TRIGGER,CONDITION)"`
	
	// 执行时间设置
	CronExpression  string    `json:"cronExpression" gorm:"column:cron_expression;type:varchar(100);comment:Cron表达式"`
	IntervalSeconds int       `json:"intervalSeconds" gorm:"column:interval_seconds;type:int;comment:间隔秒数"`
	StartTime       *time.Time `json:"startTime" gorm:"column:start_time;type:datetime;comment:开始时间"`
	EndTime         *time.Time `json:"endTime" gorm:"column:end_time;type:datetime;comment:结束时间"`
	Timezone        string    `json:"timezone" gorm:"column:timezone;type:varchar(50);default:'Asia/Shanghai';comment:时区"`
	HolidayHandle   string    `json:"holidayHandle" gorm:"column:holiday_handle;type:varchar(20);default:'SKIP';comment:节假日处理(SKIP,EXECUTE,DELAY)"`
	
	// 资源分配管理
	CpuLimit        float64   `json:"cpuLimit" gorm:"column:cpu_limit;type:decimal(5,2);comment:CPU限制(核数)"`
	MemoryLimit     int       `json:"memoryLimit" gorm:"column:memory_limit;type:int;comment:内存限制(MB)"`
	MaxConcurrency  int       `json:"maxConcurrency" gorm:"column:max_concurrency;type:int;default:1;comment:最大并发数"`
	QueuePriority   int       `json:"queuePriority" gorm:"column:queue_priority;type:int;default:5;comment:队列优先级(1-10)"`
	
	// 失败处理策略
	MaxRetryCount   int       `json:"maxRetryCount" gorm:"column:max_retry_count;type:int;default:3;comment:最大重试次数"`
	RetryInterval   int       `json:"retryInterval" gorm:"column:retry_interval;type:int;default:60;comment:重试间隔(秒)"`
	FailureNotify   string    `json:"failureNotify" gorm:"column:failure_notify;type:varchar(500);comment:失败通知配置(JSON)"`
	FallbackAction  string    `json:"fallbackAction" gorm:"column:fallback_action;type:varchar(20);default:'STOP';comment:降级处理(STOP,CONTINUE,FALLBACK)"`
	
	// 负载均衡
	LoadBalanceType string    `json:"loadBalanceType" gorm:"column:load_balance_type;type:varchar(20);default:'ROUND_ROBIN';comment:负载均衡类型"`
	
	// 状态和配置
	Status          int       `json:"status" gorm:"column:status;type:tinyint;default:1;comment:状态(0:禁用,1:启用,2:暂停)"`
	Config          string    `json:"config" gorm:"column:config;type:json;comment:扩展配置"`
}

func (ScheduleEntity) TableName() string {
	return "schedule"
}
