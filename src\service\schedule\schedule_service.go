package schedule

import (
	"errors"
	"mozhao/src/common/consts"
	"mozhao/src/common/mysql"
	"mozhao/src/common/utils"
	"mozhao/src/model/entity"
	"mozhao/src/model/vo/schedule"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

// CreateSchedule 创建调度
func CreateSchedule(context *gin.Context) (any, error) {
	var request schedule.ScheduleCreateRequest
	if err := context.ShouldBindJSON(&request); err != nil {
		return false, errors.New(consts.ParamError)
	}

	// 获取当前用户ID
	userId := utils.GetUser(context)

	// 开启事务
	tx := mysql.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建调度记录
	var scheduleEntity entity.ScheduleEntity
	copier.Copy(&scheduleEntity, &request)
	scheduleEntity.UserId = userId

	if err := tx.Create(&scheduleEntity).Error; err != nil {
		tx.Rollback()
		return false, errors.New("创建调度失败")
	}

	// 创建调度任务链
	for _, taskReq := range request.Tasks {
		var scheduleTaskEntity entity.ScheduleTaskEntity
		copier.Copy(&scheduleTaskEntity, &taskReq)
		scheduleTaskEntity.ScheduleId = scheduleEntity.Id

		if err := tx.Create(&scheduleTaskEntity).Error; err != nil {
			tx.Rollback()
			return false, errors.New("创建调度任务失败")
		}
	}

	tx.Commit()
	return scheduleEntity.Id, nil
}

// UpdateSchedule 更新调度
func UpdateSchedule(context *gin.Context) (any, error) {
	var request schedule.ScheduleUpdateRequest
	if err := context.ShouldBindJSON(&request); err != nil {
		return false, errors.New(consts.ParamError)
	}

	// 获取当前用户ID
	userId := utils.GetUser(context)

	// 检查调度是否存在且属于当前用户
	var existingSchedule entity.ScheduleEntity
	if err := mysql.Db.Where("id = ? AND user_id = ?", request.Id, userId).First(&existingSchedule).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, errors.New("调度不存在")
		}
		return false, errors.New("查询调度失败")
	}

	// 开启事务
	tx := mysql.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新调度记录
	var scheduleEntity entity.ScheduleEntity
	copier.Copy(&scheduleEntity, &request)
	scheduleEntity.UserId = userId

	if err := tx.Model(&existingSchedule).Updates(&scheduleEntity).Error; err != nil {
		tx.Rollback()
		return false, errors.New("更新调度失败")
	}

	// 删除原有的调度任务
	if err := tx.Where("schedule_id = ?", request.Id).Delete(&entity.ScheduleTaskEntity{}).Error; err != nil {
		tx.Rollback()
		return false, errors.New("删除原有调度任务失败")
	}

	// 创建新的调度任务链
	for _, taskReq := range request.Tasks {
		var scheduleTaskEntity entity.ScheduleTaskEntity
		copier.Copy(&scheduleTaskEntity, &taskReq)
		scheduleTaskEntity.ScheduleId = request.Id

		if err := tx.Create(&scheduleTaskEntity).Error; err != nil {
			tx.Rollback()
			return false, errors.New("创建调度任务失败")
		}
	}

	tx.Commit()
	return true, nil
}

// DeleteSchedule 删除调度
func DeleteSchedule(context *gin.Context) (any, error) {
	scheduleIdStr := context.Param("id")
	scheduleId, err := strconv.ParseInt(scheduleIdStr, 10, 64)
	if err != nil {
		return false, errors.New(consts.ParamError)
	}

	// 获取当前用户ID
	userId := utils.GetUser(context)

	// 检查调度是否存在且属于当前用户
	var existingSchedule entity.ScheduleEntity
	if err := mysql.Db.Where("id = ? AND user_id = ?", scheduleId, userId).First(&existingSchedule).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, errors.New("调度不存在")
		}
		return false, errors.New("查询调度失败")
	}

	// 开启事务
	tx := mysql.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除调度任务
	if err := tx.Where("schedule_id = ?", scheduleId).Delete(&entity.ScheduleTaskEntity{}).Error; err != nil {
		tx.Rollback()
		return false, errors.New("删除调度任务失败")
	}

	// 删除调度记录
	if err := tx.Delete(&existingSchedule).Error; err != nil {
		tx.Rollback()
		return false, errors.New("删除调度失败")
	}

	tx.Commit()
	return true, nil
}

// GetScheduleById 根据ID获取调度详情
func GetScheduleById(context *gin.Context) (any, error) {
	scheduleIdStr := context.Param("id")
	scheduleId, err := strconv.ParseInt(scheduleIdStr, 10, 64)
	if err != nil {
		return nil, errors.New(consts.ParamError)
	}

	// 获取当前用户ID
	userId := utils.GetUser(context)

	// 查询调度信息
	var scheduleEntity entity.ScheduleEntity
	if err := mysql.Db.Where("id = ? AND user_id = ?", scheduleId, userId).First(&scheduleEntity).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("调度不存在")
		}
		return nil, errors.New("查询调度失败")
	}

	// 查询调度任务
	var scheduleTasks []entity.ScheduleTaskEntity
	mysql.Db.Where("schedule_id = ?", scheduleId).Order("task_order").Find(&scheduleTasks)

	// 构建响应
	var response schedule.ScheduleResponse
	copier.Copy(&response, &scheduleEntity)

	// 查询任务详情并构建任务响应
	for _, scheduleTask := range scheduleTasks {
		var taskEntity entity.TaskEntity
		mysql.Db.Where("id = ?", scheduleTask.TaskId).First(&taskEntity)

		var taskResponse schedule.ScheduleTaskResponse
		copier.Copy(&taskResponse, &scheduleTask)
		taskResponse.TaskCode = taskEntity.TaskCode
		taskResponse.TaskName = taskEntity.TaskName

		response.Tasks = append(response.Tasks, taskResponse)
	}

	return response, nil
}

// GetUserSchedules 获取用户所有调度
func GetUserSchedules(context *gin.Context) (any, error) {
	// 获取当前用户ID
	userId := utils.GetUser(context)

	// 查询用户的所有调度
	var scheduleEntities []entity.ScheduleEntity
	if err := mysql.Db.Where("user_id = ?", userId).Order("create_time DESC").Find(&scheduleEntities).Error; err != nil {
		return nil, errors.New("查询调度列表失败")
	}

	var responses []schedule.ScheduleResponse
	for _, scheduleEntity := range scheduleEntities {
		// 查询调度任务
		var scheduleTasks []entity.ScheduleTaskEntity
		mysql.Db.Where("schedule_id = ?", scheduleEntity.Id).Order("task_order").Find(&scheduleTasks)

		// 构建响应
		var response schedule.ScheduleResponse
		copier.Copy(&response, &scheduleEntity)

		// 查询任务详情并构建任务响应
		for _, scheduleTask := range scheduleTasks {
			var taskEntity entity.TaskEntity
			mysql.Db.Where("id = ?", scheduleTask.TaskId).First(&taskEntity)

			var taskResponse schedule.ScheduleTaskResponse
			copier.Copy(&taskResponse, &scheduleTask)
			taskResponse.TaskCode = taskEntity.TaskCode
			taskResponse.TaskName = taskEntity.TaskName

			response.Tasks = append(response.Tasks, taskResponse)
		}

		responses = append(responses, response)
	}

	result := schedule.ScheduleListResponse{
		Schedules: responses,
		Total:     int64(len(responses)),
	}

	return result, nil
}

// GetAllTasks 获取所有可用任务
func GetAllTasks(context *gin.Context) (any, error) {
	var tasks []entity.TaskEntity
	if err := mysql.Db.Where("status = 1").Order("category, task_name").Find(&tasks).Error; err != nil {
		return nil, errors.New("查询任务列表失败")
	}

	return tasks, nil
}

// UpdateScheduleStatus 更新调度状态
func UpdateScheduleStatus(context *gin.Context) (any, error) {
	scheduleIdStr := context.Param("id")
	scheduleId, err := strconv.ParseInt(scheduleIdStr, 10, 64)
	if err != nil {
		return false, errors.New(consts.ParamError)
	}

	var request struct {
		Status int `json:"status" binding:"required"`
	}
	if err := context.ShouldBindJSON(&request); err != nil {
		return false, errors.New(consts.ParamError)
	}

	// 获取当前用户ID
	userId := utils.GetUser(context)

	// 检查调度是否存在且属于当前用户
	var existingSchedule entity.ScheduleEntity
	if err := mysql.Db.Where("id = ? AND user_id = ?", scheduleId, userId).First(&existingSchedule).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, errors.New("调度不存在")
		}
		return false, errors.New("查询调度失败")
	}

	// 更新状态
	if err := mysql.Db.Model(&existingSchedule).Update("status", request.Status).Error; err != nil {
		return false, errors.New("更新调度状态失败")
	}

	return true, nil
}
