package mysql

import (
	"fmt"
	"log"
	"mozhao/src/config"
	"os"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var Db *gorm.DB

func init() {
	err := initDB()
	if err != nil {
		panic("Failed to initialize database: " + err.Error())
	}
}

func initDB() error {
	var err error
	configData := config.ConfigData
	username := configData.Database.UserName //数据库用户名
	password := configData.Database.Password //数据库密码
	host := configData.Database.Host         //数据库主机号
	port := configData.Database.Port         //数据库端口号
	Dbname := configData.Database.Database   //数据库名称
	//root:root@tcp(127.0.0.1:3306)/gorm?
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local", username, password, host, port, Dbname)
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer
		logger.Config{
			SlowThreshold:             time.Second, // Slow SQL threshold
			LogLevel:                  logger.Info, // Log level
			IgnoreRecordNotFoundError: true,        // Ignore ErrRecordNotFound error for logger
			Colorful:                  false,       // Disable color
		},
	)
	//连接MYSQL,获得DB类型实例，用于后面的数据库读写操作
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		panic("数据库连接失败，err=" + err.Error())
	}
	//连接成功
	sqlDB, _ := db.DB()
	sqlDB.SetMaxIdleConns(2) //设置连接池，空闲
	sqlDB.SetMaxOpenConns(5) //设置打开最大连接
	Db = db
	return nil
}
