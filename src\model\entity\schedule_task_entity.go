package entity

// ScheduleTaskEntity 调度任务链表 - 存储调度中包含的任务链
type ScheduleTaskEntity struct {
	BaseEntity
	ScheduleId      int64  `json:"scheduleId" gorm:"column:schedule_id;type:bigint;not null;comment:调度ID"`
	TaskId          int64  `json:"taskId" gorm:"column:task_id;type:bigint;not null;comment:任务ID"`
	TaskOrder       int    `json:"taskOrder" gorm:"column:task_order;type:int;not null;comment:任务执行顺序"`
	
	// 依赖关系设置
	PreTaskIds      string `json:"preTaskIds" gorm:"column:pre_task_ids;type:varchar(500);comment:前置任务ID列表(逗号分隔)"`
	PostTaskIds     string `json:"postTaskIds" gorm:"column:post_task_ids;type:varchar(500);comment:后置任务ID列表(逗号分隔)"`
	DependencyType  string `json:"dependencyType" gorm:"column:dependency_type;type:varchar(20);default:'SUCCESS';comment:依赖类型(SUCCESS,FAILURE,ALWAYS,CONDITION)"`
	DependencyCondition string `json:"dependencyCondition" gorm:"column:dependency_condition;type:text;comment:依赖条件表达式"`
	
	// 任务配置
	TaskConfig      string `json:"taskConfig" gorm:"column:task_config;type:json;comment:任务配置参数"`
	TimeoutSeconds  int    `json:"timeoutSeconds" gorm:"column:timeout_seconds;type:int;default:3600;comment:任务超时时间(秒)"`
	
	// 结果传递
	OutputMapping   string `json:"outputMapping" gorm:"column:output_mapping;type:json;comment:输出映射配置"`
	InputMapping    string `json:"inputMapping" gorm:"column:input_mapping;type:json;comment:输入映射配置"`
	
	// 状态
	Status          int    `json:"status" gorm:"column:status;type:tinyint;default:1;comment:状态(0:禁用,1:启用)"`
}

func (ScheduleTaskEntity) TableName() string {
	return "schedule_task"
}
