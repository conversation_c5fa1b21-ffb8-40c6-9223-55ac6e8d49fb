# 设计文档

## 概述

数据采集系统将实现五个新的数据采集API，允许经过身份验证的用户存储来自各种社交媒体平台和数据源的结构化数据。系统遵循现有的Mozhao架构模式，采用控制器-服务-实体分层架构，使用GORM进行数据库操作，使用Gin处理HTTP请求。

## 架构设计

### 分层架构
- **控制器层**: HTTP请求处理和路由 (`src/controller/data_collection_controller.go`)
- **服务层**: 业务逻辑实现 (`src/service/data_collection/data_collection_service.go`)
- **实体层**: 数据库模型 (`src/model/entity/` - 5个新的实体文件)
- **数据库层**: MySQL配合GORM ORM

### API设计
- 基础路径: `/api/data-collection`
- 所有端点都需要使用`service.FullCheck`中间件进行JWT身份验证
- RESTful POST端点用于数据采集
- 使用现有的`utils.Result`结构保持响应格式一致性

## 组件和接口

### 数据库实体

#### 1. 用户资料数据实体 (ProfileDataEntity)
```go
type ProfileDataEntity struct {
    BaseEntity
    UserId int64 `gorm:"column:user_id;type:bigint;not null;index;comment:用户ID"`
    ProfileDataID string `gorm:"column:profile_data_id;type:varchar(255);comment:原始档案ID"`
    // Basic data fields
    Username string `gorm:"column:username;type:varchar(255);comment:用户名"`
    DisplayName string `gorm:"column:display_name;type:varchar(255);comment:显示名称"`
    Bio string `gorm:"column:bio;type:text;comment:个人简介"`
    Avatar string `gorm:"column:avatar;type:varchar(500);comment:头像URL"`
    // Statistics
    FollowerCount int64 `gorm:"column:follower_count;type:bigint;default:0;comment:粉丝数"`
    FollowingCount int64 `gorm:"column:following_count;type:bigint;default:0;comment:关注数"`
    PostCount int64 `gorm:"column:post_count;type:bigint;default:0;comment:帖子数"`
    // Verification
    Verified bool `gorm:"column:verified;type:boolean;default:false;comment:是否认证"`
    BusinessAccount bool `gorm:"column:business_account;type:boolean;default:false;comment:是否商业账户"`
    // Contact info
    Email string `gorm:"column:email;type:varchar(255);comment:邮箱"`
    Phone string `gorm:"column:phone;type:varchar(50);comment:电话"`
    Website string `gorm:"column:website;type:varchar(500);comment:网站"`
    // Location
    Country string `gorm:"column:country;type:varchar(100);comment:国家"`
    City string `gorm:"column:city;type:varchar(100);comment:城市"`
    // Professional
    Workplace string `gorm:"column:workplace;type:varchar(255);comment:工作地点"`
    Position string `gorm:"column:position;type:varchar(255);comment:职位"`
    Education string `gorm:"column:education;type:varchar(255);comment:教育背景"`
    // Metadata
    ProfileUrl string `gorm:"column:profile_url;type:varchar(500);comment:档案URL"`
    Platform string `gorm:"column:platform;type:varchar(50);comment:平台"`
    CollectedAt time.Time `gorm:"column:collected_at;type:datetime;comment:采集时间"`
}
```

#### 2. 帖子内容数据实体 (PostDataEntity)
```go
type PostDataEntity struct {
    BaseEntity
    UserId int64 `gorm:"column:user_id;type:bigint;not null;index;comment:用户ID"`
    PostDataID string `gorm:"column:post_data_id;type:varchar(255);comment:原始帖子ID"`
    // Content
    AuthorID string `gorm:"column:author_id;type:varchar(255);comment:作者ID"`
    AuthorName string `gorm:"column:author_name;type:varchar(255);comment:作者名称"`
    Content string `gorm:"column:content;type:longtext;comment:内容"`
    // Media
    MediaUrls string `gorm:"column:media_urls;type:json;comment:媒体URL列表"`
    MediaTypes string `gorm:"column:media_types;type:json;comment:媒体类型列表"`
    // Interactions
    LikeCount int64 `gorm:"column:like_count;type:bigint;default:0;comment:点赞数"`
    CommentCount int64 `gorm:"column:comment_count;type:bigint;default:0;comment:评论数"`
    ShareCount int64 `gorm:"column:share_count;type:bigint;default:0;comment:分享数"`
    ViewCount int64 `gorm:"column:view_count;type:bigint;default:0;comment:浏览数"`
    // Time
    PublishedAt time.Time `gorm:"column:published_at;type:datetime;comment:发布时间"`
    Timestamp string `gorm:"column:timestamp;type:varchar(100);comment:时间戳"`
    // Tags
    Hashtags string `gorm:"column:hashtags;type:json;comment:话题标签"`
    Mentions string `gorm:"column:mentions;type:json;comment:提及用户"`
    // Metadata
    PostUrl string `gorm:"column:post_url;type:varchar(500);comment:帖子URL"`
    Platform string `gorm:"column:platform;type:varchar(50);comment:平台"`
    CollectedAt time.Time `gorm:"column:collected_at;type:datetime;comment:采集时间"`
    IsAd bool `gorm:"column:is_ad;type:boolean;default:false;comment:是否广告"`
}
```

#### 3. 联系人数据实体 (ContactDataEntity)
```go
type ContactDataEntity struct {
    BaseEntity
    UserId int64 `gorm:"column:user_id;type:bigint;not null;index;comment:用户ID"`
    ContactDataID string `gorm:"column:contact_data_id;type:varchar(255);comment:原始联系人ID"`
    // Basic info
    Name string `gorm:"column:name;type:varchar(255);comment:姓名"`
    Username string `gorm:"column:username;type:varchar(255);comment:用户名"`
    // Contact methods
    Email string `gorm:"column:email;type:varchar(255);comment:邮箱"`
    Phone string `gorm:"column:phone;type:varchar(50);comment:电话"`
    // Professional
    Company string `gorm:"column:company;type:varchar(255);comment:公司"`
    Position string `gorm:"column:position;type:varchar(255);comment:职位"`
    // Social profiles
    Facebook string `gorm:"column:facebook;type:varchar(500);comment:Facebook"`
    Instagram string `gorm:"column:instagram;type:varchar(500);comment:Instagram"`
    Twitter string `gorm:"column:twitter;type:varchar(500);comment:Twitter"`
    LinkedIn string `gorm:"column:linkedin;type:varchar(500);comment:LinkedIn"`
    // Management
    Tags string `gorm:"column:tags;type:json;comment:标签"`
    Source string `gorm:"column:source;type:varchar(255);comment:来源"`
    Notes string `gorm:"column:notes;type:text;comment:备注"`
    // Metadata
    CollectedAt time.Time `gorm:"column:collected_at;type:datetime;comment:采集时间"`
    LastContact *time.Time `gorm:"column:last_contact;type:datetime;comment:最后联系时间"`
}
```

#### 4. 搜索结果数据实体 (SearchResultDataEntity)
```go
type SearchResultDataEntity struct {
    BaseEntity
    UserId int64 `gorm:"column:user_id;type:bigint;not null;index;comment:用户ID"`
    SearchResultDataID string `gorm:"column:search_result_data_id;type:varchar(255);comment:原始搜索结果ID"`
    // Basic info
    Name string `gorm:"column:name;type:varchar(255);comment:名称"`
    Username string `gorm:"column:username;type:varchar(255);comment:用户名"`
    // Links
    ProfileUrl string `gorm:"column:profile_url;type:varchar(500);comment:档案URL"`
    Avatar string `gorm:"column:avatar;type:varchar(500);comment:头像URL"`
    // Description
    Description string `gorm:"column:description;type:text;comment:描述"`
    Snippet string `gorm:"column:snippet;type:text;comment:摘要"`
    // Statistics
    FollowerCount int64 `gorm:"column:follower_count;type:bigint;default:0;comment:粉丝数"`
    // Search metadata
    Keyword string `gorm:"column:keyword;type:varchar(255);comment:搜索关键词"`
    Platform string `gorm:"column:platform;type:varchar(50);comment:平台"`
    Rank int `gorm:"column:rank;type:int;comment:排名"`
    CollectedAt time.Time `gorm:"column:collected_at;type:datetime;comment:采集时间"`
}
```

#### 5. 群组社区数据实体 (GroupDataEntity)
```go
type GroupDataEntity struct {
    BaseEntity
    UserId int64 `gorm:"column:user_id;type:bigint;not null;index;comment:用户ID"`
    GroupDataID string `gorm:"column:group_data_id;type:varchar(255);comment:原始群组ID"`
    // Basic info
    Name string `gorm:"column:name;type:varchar(255);comment:群组名称"`
    Description string `gorm:"column:description;type:text;comment:群组描述"`
    // Statistics
    MemberCount int64 `gorm:"column:member_count;type:bigint;default:0;comment:成员数"`
    // Properties
    IsPrivate bool `gorm:"column:is_private;type:boolean;default:false;comment:是否私有"`
    Category string `gorm:"column:category;type:varchar(100);comment:分类"`
    // Management
    AdminIds string `gorm:"column:admin_ids;type:json;comment:管理员ID列表"`
    Rules string `gorm:"column:rules;type:json;comment:群组规则"`
    // Metadata
    GroupUrl string `gorm:"column:group_url;type:varchar(500);comment:群组URL"`
    Platform string `gorm:"column:platform;type:varchar(50);comment:平台"`
    CollectedAt time.Time `gorm:"column:collected_at;type:datetime;comment:采集时间"`
}
```

### 服务层

#### 请求/响应模型
每种数据类型都将有对应的请求和响应模型，遵循现有模式:
- `AddProfileDataRequest` - 添加用户资料请求
- `AddPostDataRequest` - 添加帖子数据请求
- `AddContactDataRequest` - 添加联系人数据请求
- `AddSearchResultDataRequest` - 添加搜索结果数据请求
- `AddGroupDataRequest` - 添加群组数据请求

#### 服务函数
- `AddProfileData(ctx *gin.Context) (any, error)` - 添加用户资料数据
- `AddPostData(ctx *gin.Context) (any, error)` - 添加帖子数据
- `AddContactData(ctx *gin.Context) (any, error)` - 添加联系人数据
- `AddSearchResultData(ctx *gin.Context) (any, error)` - 添加搜索结果数据
- `AddGroupData(ctx *gin.Context) (any, error)` - 添加群组数据

### 控制器层

#### API端点
- `POST /api/data-collection/profile` - 存储用户资料数据
- `POST /api/data-collection/post` - 存储帖子数据
- `POST /api/data-collection/contact` - 存储联系人数据
- `POST /api/data-collection/search-result` - 存储搜索结果数据
- `POST /api/data-collection/group` - 存储群组数据

## 数据模型

### JSON字段处理
对于存储数组的字段 (MediaUrls, MediaTypes, Hashtags, Mentions, Tags, AdminIds, Rules)，系统将:
1. 在请求模型中接受Go切片
2. 转换为JSON字符串进行数据库存储
3. 使用GORM的JSON标签进行正确处理

### 用户关联
所有实体都将包含:
- 带有外键约束的`UserId`字段
- 使用`utils.GetUser(ctx)`从JWT令牌自动提取用户ID

### 时间戳管理
所有实体都继承自`BaseEntity`，提供:
- `Id` (使用雪花ID生成的主键)
- `CreateTime` (创建时自动生成)
- `UpdateTime` (修改时自动更新)

额外的`CollectedAt`时间戳用于业务逻辑跟踪。

## 错误处理

### 验证
- 使用Gin的`ShouldBind`进行请求结构验证
- 必填字段验证
- 数据类型验证

### 数据库错误
- 连接错误处理
- 约束违反处理
- 失败时事务回滚

### 响应格式
所有响应都遵循现有模式:
```go
// 成功
utils.Success(data, ctx)

// 错误
utils.Err(errorMessage, ctx)
```

## 测试策略

### 单元测试
- 服务层函数测试
- 请求/响应模型验证
- 数据库实体CRUD操作

### 集成测试
- 端到端API测试
- 身份验证中间件测试
- 数据库事务测试

### 测试数据
- 每种实体类型的模拟数据
- 边界情况场景 (空字段、大数据)
- 无效请求格式测试

## 安全考虑

### 身份验证
- 所有端点的JWT令牌验证
- 用户ID提取和验证
- 身份验证失败时的适当错误响应

### 数据验证
- 输入清理
- SQL注入防护 (由GORM处理)
- 文本字段的XSS防护

### 访问控制
- 用户只能访问自己的数据
- 所有操作的正确用户ID关联