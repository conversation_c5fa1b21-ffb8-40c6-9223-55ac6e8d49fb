# Product Overview

Mozhao is a Go-based web application that provides AI integration, data collection, and multi-environment configuration capabilities.

## Core Features

- **AI Integration**: Multi-model AI support including text chat, captcha recognition, and audio transcription
- **Data Collection**: Web scraping and data gathering from various platforms (Facebook, etc.)
- **User Management**: Secure authentication with RSA encryption and JWT tokens
- **Task Scheduling**: Comprehensive job scheduling system with cron expressions, dependencies, and failure handling
- **CAD Data Management**: Coordinate data storage and retrieval
- **IP Proxy Management**: Proxy service integration for data collection
- **WebSocket Support**: Real-time communication capabilities

## Target Use Cases

- Automated data collection from social media platforms
- AI-powered content analysis and processing
- Scheduled task execution and monitoring
- Multi-user web application with secure authentication
- CAD coordinate data management

## Architecture

Single-binary deployment with embedded configuration, supporting external configuration overrides and multi-environment setups (dev/prod/linux).