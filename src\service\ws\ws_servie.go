package ws

import (
	"encoding/json"
	"fmt"
	"log"
	"mozhao/src/common/consts"
	"mozhao/src/common/rdb"
	"mozhao/src/common/utils"
	"mozhao/src/service"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

type WsMessage struct {
	Method string `json:"method"`
	Data   string `json:"data"`
}

type HardwareParams struct {
	CPUUsage float64 `json:"cpuUsage"`
	Memory   struct {
		Total string `json:"total"`
		Used  string `json:"used"`
		Free  string `json:"free"`
	} `json:"memory"`
	Disks []struct {
		Mount string `json:"mount"`
		Used  string `json:"used"`
		Free  string `json:"free"`
	} `json:"disks"`
	Network struct {
		Interface string `json:"interface"`
		Download  string `json:"download"`
		Upload    string `json:"upload"`
	} `json:"network"`
	System struct {
		OS    string `json:"os"`
		Model string `json:"model"`
		Arch  string `json:"arch"`
	} `json:"system"`
	Timestamp int64 `json:"timestamp"`
}

const hardwareKeyPrefix = "hardwareUpload"

type MessageHandler func(conn *websocket.Conn, data string) error

func handleHardwareUpload(conn *websocket.Conn, data string) error {

	var params HardwareParams
	if err := json.Unmarshal([]byte(data), &params); err != nil {
		return fmt.Errorf("硬件参数解析失败: %v", err)
	}

	// 获取用户ID
	userId, exists := userConnMap[conn]
	if !exists {
		return fmt.Errorf("未找到用户连接信息")
	}

	// 生成Redis键
	redisKey := fmt.Sprintf("%s:%d", hardwareKeyPrefix, userId)

	jsonData, err := json.Marshal(params)
	if err != nil {
		return fmt.Errorf("数据序列化失败: %v", err)
	}

	// 存储到Redis
	if err := rdb.RedisClient.Set(rdb.RedisCtx, redisKey, jsonData, 0).Err(); err != nil {
		return fmt.Errorf("Redis存储失败: %v", err)
	}

	return nil
}

var (
	handlers    = make(map[string]MessageHandler)                    // 存储所有消息处理器
	userMap     = utils.CreateTypedSyncMap[int64, *websocket.Conn]() // 保证并发安全
	userConnMap = make(map[*websocket.Conn]int64)
	lock        sync.RWMutex
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool { return true }, // 允许跨域
}

// 注册 WebSocket 路由到 Gin
func RegisterWSRouter(router *gin.RouterGroup, path string) {
	InitHandlers()
	router.GET(path, func(c *gin.Context) {
		// 升级为 WebSocket 连接

		token := c.Request.Header.Get(consts.SecretHttpHead)
		conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
		if err != nil {
			log.Printf("WebSocket 升级失败: %v", err)
			return
		}

		// 为每个连接启动独立 goroutine
		go handleConnection(conn, token)
	})
}

func InitHandlers() {
	handlers["hardwareUpload"] = handleHardwareUpload
}

// 处理连接生命周期
func handleConnection(conn *websocket.Conn, token string) {
	defer conn.Close()
	OnConnect(conn, token)
	for {
		// 持续监听消息
		messageType, data, err := conn.ReadMessage()
		if err != nil {
			OnClose(conn)
			break
		}
		OnMessage(conn, messageType, data)
	}
}

func OnConnect(conn *websocket.Conn, token string) {
	lock.Lock()
	defer lock.Unlock()
	result := utils.Result{
		Code: http.StatusOK,
	}
	userId, ok := connectCheck(token, &result)
	if !ok {
		conn.WriteJSON(result)
		conn.Close()
		return
	}

	// 检查用户是否已有连接，如果有则关闭旧连接
	if oldConn, exists := userMap.Load(userId); exists {
		// 发送断开连接通知
		_ = oldConn.WriteJSON(utils.Result{
			Code: http.StatusOK,
			Msg:  "您的账号已在其他设备登录，当前设备已断开连接",
		})
		// 关闭旧连接
		oldConn.Close()
		// 删除旧连接的映射
		delete(userConnMap, oldConn)
	}

	// 存储新连接
	userMap.Store(userId, conn)
	userConnMap[conn] = userId

	conn.SetPongHandler(func(string) error {
		conn.SetReadDeadline(time.Now().Add(90 * time.Second))
		conn.SetWriteDeadline(time.Now().Add(90 * time.Second)) // 延长超时窗口
		return nil
	})
}

func connectCheck(token string, result *utils.Result) (int64, bool) {
	userId, err := service.GetLoginUserId(token)
	if err != nil {
		result.Msg = consts.NotLoginError
		return 0, false
	}
	return userId, true
}

func OnMessage(conn *websocket.Conn, messageType int, data []byte) {
	var msg WsMessage
	if err := json.Unmarshal(data, &msg); err != nil {
		log.Printf("消息解析失败: %v", err)
		conn.WriteJSON(utils.Result{Code: 400, Msg: "无效的消息格式"})
		return
	}

	handler, exists := handlers[msg.Method]
	if !exists {
		log.Printf("未找到硬件上传处理器")
		conn.WriteJSON(utils.Result{Code: 404, Msg: "未支持的方法"})
		return
	}

	if err := handler(conn, msg.Data); err != nil {
		log.Printf("处理失败: %v", err)
		conn.WriteJSON(utils.Result{Code: 500, Msg: "处理错误"})
	}
}

func OnClose(conn *websocket.Conn) {
	log.Printf("连接关闭: %s", conn.RemoteAddr())

	// 删除硬件缓存
	if userId, exists := userConnMap[conn]; exists {
		redisKey := fmt.Sprintf("%s:%d", hardwareKeyPrefix, userId)
		if err := rdb.RedisClient.Del(rdb.RedisCtx, redisKey).Err(); err != nil {
			log.Printf("Redis删除失败: %v", err)
		}
	}

	// 清理连接资源
	removeConnection(userConnMap[conn], conn)
}

func HeartHandler() {
	userMap.Range(func(key int64, conn *websocket.Conn) bool {
		if err := conn.WriteMessage(websocket.PingMessage, nil); err != nil {
			conn.Close()
		}
		return true
	})
}

func removeConnection(userId int64, conn *websocket.Conn) {
	lock.Lock()
	defer lock.Unlock()
	if userId == 0 {
		return
	}

	delete(userConnMap, conn)

	// 检查是否是当前用户的连接
	if currentConn, exists := userMap.Load(userId); exists && currentConn == conn {
		userMap.Delete(userId)
	}
}

func SendMessage(userId int64, message string) {
	conn, ok := userMap.Load(userId)
	if !ok {
		return
	}
	conn.WriteMessage(websocket.TextMessage, []byte(message))
}
