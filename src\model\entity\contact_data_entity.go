package entity

import "time"

// ContactDataEntity 联系人数据实体
type ContactDataEntity struct {
	BaseEntity
	UserId        int64      `gorm:"column:user_id;type:bigint;not null;index;comment:用户ID" json:"userId"`
	ContactDataID string     `gorm:"column:contact_data_id;type:varchar(255);comment:原始联系人ID" json:"contactDataId"`
	Name          string     `gorm:"column:name;type:varchar(255);comment:姓名" json:"name"`
	Username      string     `gorm:"column:username;type:varchar(255);comment:用户名" json:"username,omitempty"`
	Email         string     `gorm:"column:email;type:varchar(255);comment:邮箱" json:"email,omitempty"`
	Phone         string     `gorm:"column:phone;type:varchar(50);comment:电话" json:"phone,omitempty"`
	Company       string     `gorm:"column:company;type:varchar(255);comment:公司" json:"company,omitempty"`
	Position      string     `gorm:"column:position;type:varchar(255);comment:职位" json:"position,omitempty"`
	Facebook      string     `gorm:"column:facebook;type:varchar(500);comment:Facebook" json:"facebook,omitempty"`
	Instagram     string     `gorm:"column:instagram;type:varchar(500);comment:Instagram" json:"instagram,omitempty"`
	Twitter       string     `gorm:"column:twitter;type:varchar(500);comment:Twitter" json:"twitter,omitempty"`
	LinkedIn      string     `gorm:"column:linkedin;type:varchar(500);comment:LinkedIn" json:"linkedin,omitempty"`
	Tags          string     `gorm:"column:tags;type:json;comment:标签" json:"tags,omitempty"`
	Source        string     `gorm:"column:source;type:varchar(255);comment:来源" json:"source"`
	Notes         string     `gorm:"column:notes;type:text;comment:备注" json:"notes,omitempty"`
	CollectedAt   time.Time  `gorm:"column:collected_at;type:datetime;comment:采集时间" json:"collectedAt"`
	LastContact   *time.Time `gorm:"column:last_contact;type:datetime;comment:最后联系时间" json:"lastContact,omitempty"`
}

// TableName 指定表名
func (ContactDataEntity) TableName() string {
	return "contact_data"
}
