package ai

import (
	"context"
	"mozhao/src/ai"

	"github.com/gin-gonic/gin"
	// 监控指标
)

type Content struct {
	Content string `json:"content" form:"content" example:"验证码图片base64编码"`
}

type Contents struct {
	Contents []string `json:"contents" form:"contents" example:"['你好','请介绍一下自己']"`
}

func CaptchaRecognition(c *gin.Context) (any, error) {
	var content Content
	c.ShouldBind(&content)
	result, err := ai.AiChatModels.QwenVlMax.Generate(context.Background(), ai.CreateImageMessages(content.Content))
	return result.Content, err
}

func TextMessage(c *gin.Context) (any, error) {
	var contents Contents
	c.ShouldBind(&contents)
	result, err := ai.AiChatModels.DeepseekReasoner.Generate(context.Background(), ai.CreateTextMessages(contents.Contents))
	return result.Content, err
}
