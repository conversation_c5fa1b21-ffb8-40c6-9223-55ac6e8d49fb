package ai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mozhao/src/ai"
	"mozhao/src/config"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

type TranscriptionRequest struct {
	Model string `json:"model" example:"whisper-1"`
	Input struct {
		URL string `json:"url" example:"https://example.com/audio.mp3"`
	} `json:"input"`
	Parameters struct {
		Language string `json:"language" example:"zh"`
	} `json:"parameters"`
}

func createTranscriptionSendBody(url string) TranscriptionRequest {
	configData := config.ConfigData
	req := TranscriptionRequest{
		Model: configData.AudioIdentify.Model,
	}
	req.Input.URL = url
	req.Parameters.Language = "zh"
	return req
}

func sendTranscription(jsonData []byte) string {
	configData := config.ConfigData
	req, err := http.NewRequest("POST", configData.AudioIdentify.TranscriptionUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Println("Error creating request:", err)
		return ""
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", configData.AudioIdentify.Authorization)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Error sending request:", err)
		return ""
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("Error reading response:", err)
		return ""
	}

	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		fmt.Println("Error parsing JSON:", err)
		return ""
	}

	if taskID, ok := response["task_id"].(string); ok {
		return taskID
	}

	fmt.Println("task_id not found in response")
	return ""
}

func sendTask(taskId string) string {
	configData := config.ConfigData
	url := fmt.Sprintf("%s?task_id=%s", configData.AudioIdentify.TaskUrl, taskId)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		fmt.Println("Error creating request:", err)
		return ""
	}

	req.Header.Set("Authorization", configData.AudioIdentify.Authorization)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Error sending request:", err)
		return ""
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("Error reading response:", err)
		return ""
	}

	var response TaskResponse
	if err := json.Unmarshal(body, &response); err != nil {
		fmt.Println("Error parsing JSON:", err)
		return ""
	}

	if len(response.Results) > 0 && response.Results[0].SubtaskStatus == "SUCCEEDED" {
		return response.Results[0].TranscriptionURL
	}

	return ""
}

type TaskResponse struct {
	Results []Result `json:"results"`
}

type Result struct {
	FileURL          string `json:"file_url"`
	TranscriptionURL string `json:"transcription_url"`
	SubtaskStatus    string `json:"subtask_status"` // SUCCEEDED/FAILED
}

func Identify(c *gin.Context) (any, error) {
	var content Content
	c.ShouldBind(&content)
	return IdentifyUrl(content.Content)
}

func IdentifyUrl(url string) (any, error) {
	body := createTranscriptionSendBody(url)
	jsonData, _ := json.Marshal(body)
	taskId := sendTranscription(jsonData)
	time.Sleep(2 * time.Second)
	result, err := ai.AiChatModels.DeepseekReasoner.Generate(context.Background(), ai.CreateMessagesCaptchaChange(sendTask(taskId)))
	return result.Content, err
}
