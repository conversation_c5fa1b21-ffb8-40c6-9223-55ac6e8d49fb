package schedule

import "time"

// ScheduleCreateRequest 创建调度请求
type ScheduleCreateRequest struct {
	ScheduleName    string                    `json:"scheduleName" binding:"required" example:"数据同步任务"`
	Description     string                    `json:"description" example:"每日数据同步调度"`
	ScheduleType    string                    `json:"scheduleType" binding:"required" example:"CRON"`
	CronExpression  string                    `json:"cronExpression" example:"0 0 2 * * ?"`
	IntervalSeconds int                       `json:"intervalSeconds" example:"3600"`
	StartTime       *time.Time                `json:"startTime" example:"2024-01-01T00:00:00Z"`
	EndTime         *time.Time                `json:"endTime" example:"2024-12-31T23:59:59Z"`
	Timezone        string                    `json:"timezone" example:"Asia/Shanghai"`
	HolidayHandle   string                    `json:"holidayHandle" example:"SKIP"`
	CpuLimit        float64                   `json:"cpuLimit" example:"2.0"`
	MemoryLimit     int                       `json:"memoryLimit" example:"1024"`
	MaxConcurrency  int                       `json:"maxConcurrency" example:"1"`
	QueuePriority   int                       `json:"queuePriority" example:"5"`
	MaxRetryCount   int                       `json:"maxRetryCount" example:"3"`
	RetryInterval   int                       `json:"retryInterval" example:"60"`
	FailureNotify   string                    `json:"failureNotify" example:"{\"email\":\"<EMAIL>\"}"`
	FallbackAction  string                    `json:"fallbackAction" example:"STOP"`
	LoadBalanceType string                    `json:"loadBalanceType" example:"ROUND_ROBIN"`
	Config          string                    `json:"config" example:"{}"`
	Tasks           []ScheduleTaskCreateRequest `json:"tasks"`
}

// ScheduleTaskCreateRequest 创建调度任务请求
type ScheduleTaskCreateRequest struct {
	TaskId              int64  `json:"taskId" binding:"required" example:"1"`
	TaskOrder           int    `json:"taskOrder" binding:"required" example:"1"`
	PreTaskIds          string `json:"preTaskIds" example:""`
	PostTaskIds         string `json:"postTaskIds" example:""`
	DependencyType      string `json:"dependencyType" example:"SUCCESS"`
	DependencyCondition string `json:"dependencyCondition" example:""`
	TaskConfig          string `json:"taskConfig" example:"{}"`
	TimeoutSeconds      int    `json:"timeoutSeconds" example:"3600"`
	OutputMapping       string `json:"outputMapping" example:"{}"`
	InputMapping        string `json:"inputMapping" example:"{}"`
}

// ScheduleUpdateRequest 更新调度请求
type ScheduleUpdateRequest struct {
	Id              int64                     `json:"id" binding:"required"`
	ScheduleName    string                    `json:"scheduleName" binding:"required"`
	Description     string                    `json:"description"`
	ScheduleType    string                    `json:"scheduleType" binding:"required"`
	CronExpression  string                    `json:"cronExpression"`
	IntervalSeconds int                       `json:"intervalSeconds"`
	StartTime       *time.Time                `json:"startTime"`
	EndTime         *time.Time                `json:"endTime"`
	Timezone        string                    `json:"timezone"`
	HolidayHandle   string                    `json:"holidayHandle"`
	CpuLimit        float64                   `json:"cpuLimit"`
	MemoryLimit     int                       `json:"memoryLimit"`
	MaxConcurrency  int                       `json:"maxConcurrency"`
	QueuePriority   int                       `json:"queuePriority"`
	MaxRetryCount   int                       `json:"maxRetryCount"`
	RetryInterval   int                       `json:"retryInterval"`
	FailureNotify   string                    `json:"failureNotify"`
	FallbackAction  string                    `json:"fallbackAction"`
	LoadBalanceType string                    `json:"loadBalanceType"`
	Config          string                    `json:"config"`
	Status          int                       `json:"status"`
	Tasks           []ScheduleTaskUpdateRequest `json:"tasks"`
}

// ScheduleTaskUpdateRequest 更新调度任务请求
type ScheduleTaskUpdateRequest struct {
	Id                  int64  `json:"id"`
	TaskId              int64  `json:"taskId" binding:"required"`
	TaskOrder           int    `json:"taskOrder" binding:"required"`
	PreTaskIds          string `json:"preTaskIds"`
	PostTaskIds         string `json:"postTaskIds"`
	DependencyType      string `json:"dependencyType"`
	DependencyCondition string `json:"dependencyCondition"`
	TaskConfig          string `json:"taskConfig"`
	TimeoutSeconds      int    `json:"timeoutSeconds"`
	OutputMapping       string `json:"outputMapping"`
	InputMapping        string `json:"inputMapping"`
	Status              int    `json:"status"`
}

// ScheduleResponse 调度响应
type ScheduleResponse struct {
	Id              int64                  `json:"id"`
	ScheduleName    string                 `json:"scheduleName"`
	Description     string                 `json:"description"`
	ScheduleType    string                 `json:"scheduleType"`
	CronExpression  string                 `json:"cronExpression"`
	IntervalSeconds int                    `json:"intervalSeconds"`
	StartTime       *time.Time             `json:"startTime"`
	EndTime         *time.Time             `json:"endTime"`
	Timezone        string                 `json:"timezone"`
	HolidayHandle   string                 `json:"holidayHandle"`
	CpuLimit        float64                `json:"cpuLimit"`
	MemoryLimit     int                    `json:"memoryLimit"`
	MaxConcurrency  int                    `json:"maxConcurrency"`
	QueuePriority   int                    `json:"queuePriority"`
	MaxRetryCount   int                    `json:"maxRetryCount"`
	RetryInterval   int                    `json:"retryInterval"`
	FailureNotify   string                 `json:"failureNotify"`
	FallbackAction  string                 `json:"fallbackAction"`
	LoadBalanceType string                 `json:"loadBalanceType"`
	Config          string                 `json:"config"`
	Status          int                    `json:"status"`
	CreateTime      *time.Time             `json:"createTime"`
	UpdateTime      *time.Time             `json:"updateTime"`
	Tasks           []ScheduleTaskResponse `json:"tasks"`
}

// ScheduleTaskResponse 调度任务响应
type ScheduleTaskResponse struct {
	Id                  int64      `json:"id"`
	TaskId              int64      `json:"taskId"`
	TaskCode            string     `json:"taskCode"`
	TaskName            string     `json:"taskName"`
	TaskOrder           int        `json:"taskOrder"`
	PreTaskIds          string     `json:"preTaskIds"`
	PostTaskIds         string     `json:"postTaskIds"`
	DependencyType      string     `json:"dependencyType"`
	DependencyCondition string     `json:"dependencyCondition"`
	TaskConfig          string     `json:"taskConfig"`
	TimeoutSeconds      int        `json:"timeoutSeconds"`
	OutputMapping       string     `json:"outputMapping"`
	InputMapping        string     `json:"inputMapping"`
	Status              int        `json:"status"`
	CreateTime          *time.Time `json:"createTime"`
	UpdateTime          *time.Time `json:"updateTime"`
}

// ScheduleListResponse 调度列表响应
type ScheduleListResponse struct {
	Schedules []ScheduleResponse `json:"schedules"`
	Total     int64              `json:"total"`
}
