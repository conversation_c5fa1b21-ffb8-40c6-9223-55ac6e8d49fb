package entity

import "time"

// PostDataEntity 帖子内容数据实体
type PostDataEntity struct {
	BaseEntity
	UserId       int64     `gorm:"column:user_id;type:bigint;not null;index;comment:用户ID" json:"userId"`
	PostDataID   string    `gorm:"column:post_data_id;type:varchar(255);comment:原始帖子ID" json:"postDataId"`
	AuthorID     string    `gorm:"column:author_id;type:varchar(255);comment:作者ID" json:"authorId"`
	AuthorName   string    `gorm:"column:author_name;type:varchar(255);comment:作者名称" json:"authorName"`
	Content      string    `gorm:"column:content;type:longtext;comment:内容" json:"content"`
	MediaUrls    string    `gorm:"column:media_urls;type:json;comment:媒体URL列表" json:"mediaUrls,omitempty"`
	MediaTypes   string    `gorm:"column:media_types;type:json;comment:媒体类型列表" json:"mediaTypes,omitempty"`
	LikeCount    int64     `gorm:"column:like_count;type:bigint;default:0;comment:点赞数" json:"likeCount,omitempty"`
	CommentCount int64     `gorm:"column:comment_count;type:bigint;default:0;comment:评论数" json:"commentCount,omitempty"`
	ShareCount   int64     `gorm:"column:share_count;type:bigint;default:0;comment:分享数" json:"shareCount,omitempty"`
	ViewCount    int64     `gorm:"column:view_count;type:bigint;default:0;comment:浏览数" json:"viewCount,omitempty"`
	PublishedAt  time.Time `gorm:"column:published_at;type:datetime;comment:发布时间" json:"publishedAt"`
	Timestamp    string    `gorm:"column:timestamp;type:varchar(100);comment:时间戳" json:"timestamp"`
	Hashtags     string    `gorm:"column:hashtags;type:json;comment:话题标签" json:"hashtags,omitempty"`
	Mentions     string    `gorm:"column:mentions;type:json;comment:提及用户" json:"mentions,omitempty"`
	PostUrl      string    `gorm:"column:post_url;type:varchar(500);comment:帖子URL" json:"postUrl,omitempty"`
	Platform     string    `gorm:"column:platform;type:varchar(50);comment:平台" json:"platform"`
	CollectedAt  time.Time `gorm:"column:collected_at;type:datetime;comment:采集时间" json:"collectedAt"`
	IsAd         bool      `gorm:"column:is_ad;type:boolean;default:false;comment:是否广告" json:"isAd,omitempty"`
}

// TableName 指定表名
func (PostDataEntity) TableName() string {
	return "post_data"
}
