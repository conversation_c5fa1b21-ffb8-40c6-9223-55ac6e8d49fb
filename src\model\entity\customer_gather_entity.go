package entity

import (
	"time"
)

type CustomerGatherEntity struct {
	BaseEntity
	UserId       int64     `gorm:"column:user_id" json:"userId"`             // 用户ID，允许NULL
	PlatformId   int64     `gorm:"column:platform_id" json:"platformId"`     // 平台ID，允许NULL
	PlatformCode string    `gorm:"column:platform_code" json:"platformCode"` // 平台编码，最大长度100
	GatherTime   time.Time `gorm:"column:gather_time" json:"gatherTime"`     // 采集时间，允许NULL
	GatherType   string    `gorm:"column:gather_type" json:"gatherType"`     // 采集类型，最大长度100
	SameId       int64     `gorm:"column:same_id" json:"sameId"`             // 关联ID，允许NULL
	KeyWord      string    `gorm:"column:key_word" json:"keyWord"`
}

// TableName 设置数据库表名
func (CustomerGatherEntity) TableName() string {
	return "customer_gather"
}
