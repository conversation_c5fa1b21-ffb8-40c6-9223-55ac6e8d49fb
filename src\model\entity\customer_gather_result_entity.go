package entity

type CustomerGatherResultEntity struct {
	BaseEntity
	Url             string `gorm:"column:url"` // 使用指针处理 NULL
	AccountId       string `gorm:"column:account_id"`
	AccountName     string `gorm:"column:account_name"`
	AccountSynopsis string `gorm:"column:account_synopsis"`
	GatherId        int64  `gorm:"column:gather_id"`
}

// TableName 设置数据库表名
func (CustomerGatherResultEntity) TableName() string {
	return "customer_gather_result"
}
