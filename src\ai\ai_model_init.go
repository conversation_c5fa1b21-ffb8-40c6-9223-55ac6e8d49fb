package ai

import (
	"context"
	"fmt"
	"log"
	"mozhao/src/config"

	chatOpenAi "github.com/cloudwego/eino-ext/components/model/openai"
	"github.com/cloudwego/eino/components/model"
)

// 模型标识常量，便于引用和管理
const (
	ModelDeepSeekR1DistillQwen7B  = "DeepSeekR1DistillQwen7B"
	ModelDeepseekAiDeepseekVl2    = "DeepseekAiDeepseekVl2"
	ModelProQwenQwen2VL7BInstruct = "ProQwenQwen2VL7BInstruct"
	ModelQwenVlMax                = "QwenVlMax"
	ModelDeepseekReasoner         = "DeepseekReasoner"
	ModelDeepseekChat             = "DeepseekChat"
)

// ModelConfig 统一模型配置结构
type ModelConfig struct {
	URL    string
	Model  string
	APIKey string
}

// AiChatModel 模型集合结构体
type AiChatModel struct {
	DeepSeekR1DistillQwen7B  model.ChatModel
	DeepseekAiDeepseekVl2    model.ChatModel
	ProQwenQwen2VL7BInstruct model.ChatModel
	QwenVlMax                model.ChatModel
	DeepseekReasoner         model.ChatModel
	DeepseekChat             model.ChatModel
}

var AiChatModels AiChatModel

type AiEembedderModel struct {
}

var AiEembedderModels AiEembedderModel

// init 初始化所有AI模型
func init() {
	ctx := context.Background()

	// 定义模型初始化映射
	modelConfigs := map[string]struct {
		config ModelConfig
		field  *model.ChatModel
	}{
		ModelDeepSeekR1DistillQwen7B: {
			config: getModelConfig(ModelDeepSeekR1DistillQwen7B),
			field:  &AiChatModels.DeepSeekR1DistillQwen7B,
		},
		ModelDeepseekAiDeepseekVl2: {
			config: getModelConfig(ModelDeepseekAiDeepseekVl2),
			field:  &AiChatModels.DeepseekAiDeepseekVl2,
		},
		ModelProQwenQwen2VL7BInstruct: {
			config: getModelConfig(ModelProQwenQwen2VL7BInstruct),
			field:  &AiChatModels.ProQwenQwen2VL7BInstruct,
		},
		ModelQwenVlMax: {
			config: getModelConfig(ModelQwenVlMax),
			field:  &AiChatModels.QwenVlMax,
		},
		ModelDeepseekReasoner: {
			config: getModelConfig(ModelDeepseekReasoner),
			field:  &AiChatModels.DeepseekReasoner,
		},
		ModelDeepseekChat: {
			config: getModelConfig(ModelDeepseekChat),
			field:  &AiChatModels.DeepseekChat,
		},
	}

	// 批量初始化模型
	for name, info := range modelConfigs {
		model, err := createChatModel(ctx, info.config)
		if err != nil {
			log.Printf("警告: 初始化AI模型 %s 失败: %v", name, err)
			continue // 继续初始化其他模型，而不是中断程序
		}
		*info.field = model // 使用指针直接赋值
	}
}

// getModelConfig 根据模型名称获取配置
func getModelConfig(modelName string) ModelConfig {
	configData := config.ConfigData.Ai

	switch modelName {
	case ModelDeepSeekR1DistillQwen7B:
		return ModelConfig{
			URL:    configData.DeepSeekR1DistillQwen7B.Url,
			Model:  configData.DeepSeekR1DistillQwen7B.Model,
			APIKey: configData.DeepSeekR1DistillQwen7B.Authorization,
		}
	case ModelDeepseekAiDeepseekVl2:
		return ModelConfig{
			URL:    configData.DeepseekAiDeepseekVl2.Url,
			Model:  configData.DeepseekAiDeepseekVl2.Model,
			APIKey: configData.DeepseekAiDeepseekVl2.Authorization,
		}
	case ModelProQwenQwen2VL7BInstruct:
		return ModelConfig{
			URL:    configData.ProQwenQwen2VL7BInstruct.Url,
			Model:  configData.ProQwenQwen2VL7BInstruct.Model,
			APIKey: configData.ProQwenQwen2VL7BInstruct.Authorization,
		}
	case ModelQwenVlMax:
		return ModelConfig{
			URL:    configData.QwenVlMax.Url,
			Model:  configData.QwenVlMax.Model,
			APIKey: configData.QwenVlMax.Authorization,
		}
	case ModelDeepseekReasoner:
		return ModelConfig{
			URL:    configData.DeepseekReasoner.Url,
			Model:  configData.DeepseekReasoner.Model,
			APIKey: configData.DeepseekReasoner.Authorization,
		}
	case ModelDeepseekChat:
		return ModelConfig{
			URL:    configData.DeepseekChat.Url,
			Model:  configData.DeepseekChat.Model,
			APIKey: configData.DeepseekChat.Authorization,
		}
	default:
		log.Printf("未知的模型名称: %s", modelName)
		return ModelConfig{}
	}
}

// createChatModel 通用的模型创建函数
func createChatModel(ctx context.Context, config ModelConfig) (model.ChatModel, error) {
	// 参数校验
	if config.URL == "" || config.Model == "" || config.APIKey == "" {
		return nil, fmt.Errorf("模型配置信息不完整")
	}

	chatModel, err := chatOpenAi.NewChatModel(ctx, &chatOpenAi.ChatModelConfig{
		BaseURL: config.URL,
		Model:   config.Model,
		APIKey:  config.APIKey,
	})

	if err != nil {
		return nil, fmt.Errorf("创建AI模型失败: %v", err)
	}

	return chatModel, nil
}
