package config

type Config struct {
	Server struct {
		Path string `mapstructure:"path" validate:"required"`
		Port string `mapstructure:"port" validate:"required"`
	}
	Database struct {
		Host     string `mapstructure:"host" validate:"required"`
		Port     string `mapstructure:"port" validate:"required"`
		UserName string `mapstructure:"userName" validate:"required"`
		Password string `mapstructure:"password" validate:"required"`
		Database string `mapstructure:"database" validate:"required"`
	}
	Redis struct {
		Addr     string `mapstructure:"addr" validate:"required"`
		Password string `mapstructure:"password" validate:"required"`
		Db       int    `mapstructure:"db" validate:"required"`
	}
	Jwt struct {
		Key string `mapstructure:"key" validate:"required"`
	}
	Ai struct {
		DeepSeekR1DistillQwen7B struct {
			Url           string `mapstructure:"url" validate:"required"`
			Model         string `mapstructure:"model" validate:"required"`
			Authorization string `mapstructure:"authorization" validate:"required"`
		}
		DeepseekAiDeepseekVl2 struct {
			Url           string `mapstructure:"url" validate:"required"`
			Model         string `mapstructure:"model" validate:"required"`
			Authorization string `mapstructure:"authorization" validate:"required"`
		}
		ProQwenQwen2VL7BInstruct struct {
			Url           string `mapstructure:"url" validate:"required"`
			Model         string `mapstructure:"model" validate:"required"`
			Authorization string `mapstructure:"authorization" validate:"required"`
		}
		QwenVlMax struct {
			Url           string `mapstructure:"url" validate:"required"`
			Model         string `mapstructure:"model" validate:"required"`
			Authorization string `mapstructure:"authorization" validate:"required"`
		}
		DeepseekReasoner struct {
			Url           string `mapstructure:"url" validate:"required"`
			Model         string `mapstructure:"model" validate:"required"`
			Authorization string `mapstructure:"authorization" validate:"required"`
		}
		DeepseekChat struct {
			Url           string `mapstructure:"url" validate:"required"`
			Model         string `mapstructure:"model" validate:"required"`
			Authorization string `mapstructure:"authorization" validate:"required"`
		}
	}
	AudioIdentify struct {
		TranscriptionUrl string `mapstructure:"transcriptionUrl" validate:"required"`
		TaskUrl          string `mapstructure:"taskUrl" validate:"required"`
		Authorization    string `mapstructure:"authorization" validate:"required"`
		Model            string `mapstructure:"model" validate:"required"`
	}
}

var ConfigData Config
