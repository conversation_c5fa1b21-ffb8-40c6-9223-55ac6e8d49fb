{"swagger": "2.0", "info": {"description": "Mozhao 服务端 API 文档", "title": "Mozhao Server API", "contact": {}, "version": "1.0"}, "host": "localhost:8080", "basePath": "/api", "paths": {"/api/ai/captchaRecognition": {"post": {"description": "使用AI识别验证码图片内容", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["ai"], "summary": "验证码识别", "parameters": [{"description": "验证码图片内容", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ai.Content"}}], "responses": {"200": {"description": "识别结果", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"type": "string"}}}]}}}}}, "/api/ai/identify": {"post": {"description": "识别音频文件内容", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["ai"], "summary": "音频识别", "parameters": [{"description": "音频识别请求", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ai.TranscriptionRequest"}}], "responses": {"200": {"description": "识别结果", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"type": "string"}}}]}}}}}, "/api/ai/textMessage": {"post": {"description": "与AI进行多轮文本对话", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["ai"], "summary": "AI文本对话", "parameters": [{"description": "对话内容列表", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ai.Contents"}}], "responses": {"200": {"description": "AI回复内容", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"type": "string"}}}]}}}}}, "/api/cad/add": {"post": {"description": "批量添加CAD坐标点数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["cad"], "summary": "添加CAD坐标数据", "parameters": [{"description": "CAD数据列表", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cad.addCadModel"}}], "responses": {"200": {"description": "添加结果", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"type": "boolean"}}}]}}}}}, "/api/cad/query": {"get": {"description": "获取所有CAD坐标数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["cad"], "summary": "查询CAD数据列表", "responses": {"200": {"description": "CAD数据列表", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/entity.CadEntity"}}}}]}}}}}, "/api/gather/add": {"post": {"security": [{"Bearer": []}], "description": "新建一个网页内容采集任务", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["gather"], "summary": "创建采集任务", "parameters": [{"description": "采集任务信息", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gather.AddGatherRequest"}}], "responses": {"200": {"description": "创建结果", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"type": "boolean"}}}]}}}}}, "/api/gather/query": {"get": {"security": [{"Bearer": []}], "description": "分页查询采集任务列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["gather"], "summary": "查询采集列表", "parameters": [{"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页数量", "name": "pageSize", "in": "query", "required": true}, {"type": "string", "description": "采集类型", "name": "gatherType", "in": "query"}, {"type": "string", "description": "关键词", "name": "key<PERSON>ord", "in": "query"}, {"type": "string", "description": "平台编码", "name": "platformCode", "in": "query"}], "responses": {"200": {"description": "分页查询结果", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/gather.GatherPageResult"}}}]}}}}}, "/api/gather/queryInfo": {"get": {"security": [{"Bearer": []}], "description": "根据ID获取采集任务详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["gather"], "summary": "获取采集详情", "parameters": [{"type": "string", "description": "采集任务ID", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "采集详情，包含gather和result字段", "schema": {"$ref": "#/definitions/utils.Result"}}}}}, "/api/ip/getIp": {"post": {"description": "根据地址查询可用的IP代理信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["ip"], "summary": "查询IP代理信息", "parameters": [{"description": "地址信息", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ip.address"}}], "responses": {"200": {"description": "IP代理信息", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/ipdata.IpData"}}}]}}}}}, "/api/job/browserStart": {"post": {"security": [{"Bearer": []}], "description": "为指定平台用户启动浏览器会话", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["job"], "summary": "启动浏览器任务", "parameters": [{"description": "浏览器启动请求", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/job.WebBrowserStartRequest"}}], "responses": {"200": {"description": "启动结果", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"type": "boolean"}}}]}}}}}, "/api/schedule/create": {"post": {"security": [{"Bearer": []}], "description": "创建新的任务调度配置", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["schedule"], "summary": "创建调度", "parameters": [{"description": "调度创建请求", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schedule.ScheduleCreateRequest"}}], "responses": {"200": {"description": "创建成功返回调度ID", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"type": "integer", "format": "int64"}}}]}}}}}, "/api/schedule/list": {"get": {"security": [{"Bearer": []}], "description": "获取当前用户的所有调度配置列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["schedule"], "summary": "获取用户所有调度", "responses": {"200": {"description": "调度列表", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schedule.ScheduleListResponse"}}}]}}}}}, "/api/schedule/tasks": {"get": {"security": [{"Bearer": []}], "description": "获取系统中所有可用的任务类型", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["schedule"], "summary": "获取所有可用任务", "responses": {"200": {"description": "任务列表", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/entity.TaskEntity"}}}}]}}}}}, "/api/schedule/update": {"put": {"security": [{"Bearer": []}], "description": "更新现有的任务调度配置", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["schedule"], "summary": "更新调度", "parameters": [{"description": "调度更新请求", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/schedule.ScheduleUpdateRequest"}}], "responses": {"200": {"description": "更新结果", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"type": "boolean"}}}]}}}}}, "/api/schedule/{id}": {"get": {"security": [{"Bearer": []}], "description": "根据ID获取调度的详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["schedule"], "summary": "获取调度详情", "parameters": [{"type": "integer", "format": "int64", "description": "调度ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "调度详情", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/schedule.ScheduleResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定的任务调度配置", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["schedule"], "summary": "删除调度", "parameters": [{"type": "integer", "format": "int64", "description": "调度ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除结果", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"type": "boolean"}}}]}}}}}, "/api/schedule/{id}/status": {"put": {"security": [{"Bearer": []}], "description": "启用、禁用或暂停调度", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["schedule"], "summary": "更新调度状态", "parameters": [{"type": "integer", "format": "int64", "description": "调度ID", "name": "id", "in": "path", "required": true}, {"description": "状态更新请求", "name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"status": {"type": "integer"}}}}], "responses": {"200": {"description": "更新结果", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"type": "boolean"}}}]}}}}}, "/api/user/addPlatform": {"post": {"security": [{"Bearer": []}], "description": "为用户添加第三方平台账号信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "添加平台账号", "parameters": [{"description": "加密的平台账号信息", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/user.userPlatformSecret"}}], "responses": {"200": {"description": "添加结果", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"type": "boolean"}}}]}}}}}, "/api/user/getPublicKey": {"post": {"description": "获取用于加密的RSA公钥", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "获取RSA公钥", "responses": {"200": {"description": "RSA公钥信息", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/user.rsaPublic"}}}]}}}}}, "/api/user/login": {"post": {"description": "通过加密账号密码登录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "登录", "parameters": [{"description": "加密的用户登录信息", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/user.userSecret"}}], "responses": {"200": {"description": "登录成功返回用户信息和token", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/user.LoginVo"}}}]}}}}}, "/api/user/register": {"post": {"description": "通过加密账号密码注册", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "注册", "parameters": [{"description": "加密的用户注册信息", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/user.userSecret"}}], "responses": {"200": {"description": "注册结果", "schema": {"allOf": [{"$ref": "#/definitions/utils.Result"}, {"type": "object", "properties": {"data": {"type": "boolean"}}}]}}}}}}, "definitions": {"ai.Content": {"type": "object", "properties": {"content": {"type": "string", "example": "验证码图片base64编码"}}}, "ai.Contents": {"type": "object", "properties": {"contents": {"type": "array", "items": {"type": "string"}, "example": ["['你好'", "'请介绍一下自己']"]}}}, "ai.TranscriptionRequest": {"type": "object", "properties": {"input": {"type": "object", "properties": {"url": {"type": "string", "example": "https://example.com/audio.mp3"}}}, "model": {"type": "string", "example": "whisper-1"}, "parameters": {"type": "object", "properties": {"language": {"type": "string", "example": "zh"}}}}}, "cad.addCadModel": {"type": "object", "properties": {"cads": {"type": "array", "items": {"$ref": "#/definitions/cad.cadModel"}}}}, "cad.cadModel": {"type": "object", "properties": {"url": {"type": "string", "example": "https://example.com/image.jpg"}, "x": {"type": "number", "example": 100.5}, "y": {"type": "number", "example": 200.3}}}, "entity.CadEntity": {"type": "object", "properties": {"cad_id": {"description": "允许NULL的bigint", "type": "integer"}, "id": {"type": "integer"}, "url": {"description": "长文本URL", "type": "string"}, "x": {"description": "decimal(10,4)", "type": "number"}, "y": {"description": "decimal(10,4)", "type": "number"}}}, "entity.CustomerGatherEntity": {"type": "object", "properties": {"gatherTime": {"description": "采集时间，允许NULL", "type": "string"}, "gatherType": {"description": "采集类型，最大长度100", "type": "string"}, "id": {"type": "integer"}, "keyWord": {"type": "string"}, "platformCode": {"description": "平台编码，最大长度100", "type": "string"}, "platformId": {"description": "平台ID，允许NULL", "type": "integer"}, "sameId": {"description": "关联ID，允许NULL", "type": "integer"}, "userId": {"description": "用户ID，允许NULL", "type": "integer"}}}, "entity.TaskEntity": {"type": "object", "properties": {"category": {"type": "string"}, "defaultConfig": {"type": "string"}, "id": {"type": "integer"}, "status": {"type": "integer"}, "taskCode": {"type": "string"}, "taskDescription": {"type": "string"}, "taskName": {"type": "string"}, "taskType": {"type": "string"}}}, "gather.AddGatherRequest": {"type": "object", "properties": {"gatherTime": {"description": "采集时间，允许NULL", "type": "string", "example": "2024-01-01T00:00:00Z"}, "gatherType": {"description": "采集类型，最大长度100", "type": "string", "example": "用户信息"}, "keyWord": {"type": "string", "example": "关键词"}, "platformCode": {"description": "平台编码，最大长度100", "type": "string", "example": "FB"}, "platformId": {"description": "平台ID，允许NULL", "type": "integer", "example": 123456}, "results": {"type": "array", "items": {"$ref": "#/definitions/gather.AddGatherResultRequest"}}}}, "gather.AddGatherResultRequest": {"type": "object", "properties": {"accountId": {"type": "string", "example": "user123"}, "accountName": {"type": "string", "example": "用户名"}, "accountSynopsis": {"type": "string", "example": "用户简介"}, "url": {"type": "string", "example": "https://example.com/profile"}}}, "gather.GatherPageResult": {"type": "object", "properties": {"list": {"description": "数据列表", "type": "array", "items": {"$ref": "#/definitions/entity.CustomerGatherEntity"}}, "page": {"description": "当前页码", "type": "integer", "example": 1}, "pageSize": {"description": "每页数量", "type": "integer", "example": 10}, "total": {"description": "总记录数", "type": "integer", "example": 100}, "totalPage": {"description": "总页数", "type": "integer", "example": 10}}}, "ip.address": {"type": "object", "required": ["address"], "properties": {"address": {"type": "string", "example": "北京"}}}, "ipdata.IpData": {"type": "object", "properties": {"ip": {"type": "string", "example": "***********"}, "port": {"type": "string", "example": "8080"}}}, "job.WebBrowserStartRequest": {"type": "object", "required": ["platformCode", "platformUserName"], "properties": {"platformCode": {"type": "string", "example": "FB"}, "platformUserName": {"type": "string", "example": "testuser"}}}, "schedule.ScheduleCreateRequest": {"type": "object", "required": ["scheduleName", "scheduleType"], "properties": {"config": {"type": "string", "example": "{}"}, "cpuLimit": {"type": "number", "example": 2}, "cronExpression": {"type": "string", "example": "0 0 2 * * ?"}, "description": {"type": "string", "example": "每日数据同步调度"}, "endTime": {"type": "string", "example": "2024-12-31T23:59:59Z"}, "failureNotify": {"type": "string", "example": "{\"email\":\"<EMAIL>\"}"}, "fallbackAction": {"type": "string", "example": "STOP"}, "holidayHandle": {"type": "string", "example": "SKIP"}, "intervalSeconds": {"type": "integer", "example": 3600}, "loadBalanceType": {"type": "string", "example": "ROUND_ROBIN"}, "maxConcurrency": {"type": "integer", "example": 1}, "maxRetryCount": {"type": "integer", "example": 3}, "memoryLimit": {"type": "integer", "example": 1024}, "queuePriority": {"type": "integer", "example": 5}, "retryInterval": {"type": "integer", "example": 60}, "scheduleName": {"type": "string", "example": "数据同步任务"}, "scheduleType": {"type": "string", "example": "CRON"}, "startTime": {"type": "string", "example": "2024-01-01T00:00:00Z"}, "tasks": {"type": "array", "items": {"$ref": "#/definitions/schedule.ScheduleTaskCreateRequest"}}, "timezone": {"type": "string", "example": "Asia/Shanghai"}}}, "schedule.ScheduleListResponse": {"type": "object", "properties": {"schedules": {"type": "array", "items": {"$ref": "#/definitions/schedule.ScheduleResponse"}}, "total": {"type": "integer"}}}, "schedule.ScheduleResponse": {"type": "object", "properties": {"config": {"type": "string"}, "cpuLimit": {"type": "number"}, "createTime": {"type": "string"}, "cronExpression": {"type": "string"}, "description": {"type": "string"}, "endTime": {"type": "string"}, "failureNotify": {"type": "string"}, "fallbackAction": {"type": "string"}, "holidayHandle": {"type": "string"}, "id": {"type": "integer"}, "intervalSeconds": {"type": "integer"}, "loadBalanceType": {"type": "string"}, "maxConcurrency": {"type": "integer"}, "maxRetryCount": {"type": "integer"}, "memoryLimit": {"type": "integer"}, "queuePriority": {"type": "integer"}, "retryInterval": {"type": "integer"}, "scheduleName": {"type": "string"}, "scheduleType": {"type": "string"}, "startTime": {"type": "string"}, "status": {"type": "integer"}, "tasks": {"type": "array", "items": {"$ref": "#/definitions/schedule.ScheduleTaskResponse"}}, "timezone": {"type": "string"}, "updateTime": {"type": "string"}}}, "schedule.ScheduleTaskCreateRequest": {"type": "object", "required": ["taskId", "taskOrder"], "properties": {"dependencyCondition": {"type": "string", "example": ""}, "dependencyType": {"type": "string", "example": "SUCCESS"}, "inputMapping": {"type": "string", "example": "{}"}, "outputMapping": {"type": "string", "example": "{}"}, "postTaskIds": {"type": "string", "example": ""}, "preTaskIds": {"type": "string", "example": ""}, "taskConfig": {"type": "string", "example": "{}"}, "taskId": {"type": "integer", "example": 1}, "taskOrder": {"type": "integer", "example": 1}, "timeoutSeconds": {"type": "integer", "example": 3600}}}, "schedule.ScheduleTaskResponse": {"type": "object", "properties": {"createTime": {"type": "string"}, "dependencyCondition": {"type": "string"}, "dependencyType": {"type": "string"}, "id": {"type": "integer"}, "inputMapping": {"type": "string"}, "outputMapping": {"type": "string"}, "postTaskIds": {"type": "string"}, "preTaskIds": {"type": "string"}, "status": {"type": "integer"}, "taskCode": {"type": "string"}, "taskConfig": {"type": "string"}, "taskId": {"type": "integer"}, "taskName": {"type": "string"}, "taskOrder": {"type": "integer"}, "timeoutSeconds": {"type": "integer"}, "updateTime": {"type": "string"}}}, "schedule.ScheduleTaskUpdateRequest": {"type": "object", "required": ["taskId", "taskOrder"], "properties": {"dependencyCondition": {"type": "string"}, "dependencyType": {"type": "string"}, "id": {"type": "integer"}, "inputMapping": {"type": "string"}, "outputMapping": {"type": "string"}, "postTaskIds": {"type": "string"}, "preTaskIds": {"type": "string"}, "status": {"type": "integer"}, "taskConfig": {"type": "string"}, "taskId": {"type": "integer"}, "taskOrder": {"type": "integer"}, "timeoutSeconds": {"type": "integer"}}}, "schedule.ScheduleUpdateRequest": {"type": "object", "required": ["id", "scheduleName", "scheduleType"], "properties": {"config": {"type": "string"}, "cpuLimit": {"type": "number"}, "cronExpression": {"type": "string"}, "description": {"type": "string"}, "endTime": {"type": "string"}, "failureNotify": {"type": "string"}, "fallbackAction": {"type": "string"}, "holidayHandle": {"type": "string"}, "id": {"type": "integer"}, "intervalSeconds": {"type": "integer"}, "loadBalanceType": {"type": "string"}, "maxConcurrency": {"type": "integer"}, "maxRetryCount": {"type": "integer"}, "memoryLimit": {"type": "integer"}, "queuePriority": {"type": "integer"}, "retryInterval": {"type": "integer"}, "scheduleName": {"type": "string"}, "scheduleType": {"type": "string"}, "startTime": {"type": "string"}, "status": {"type": "integer"}, "tasks": {"type": "array", "items": {"$ref": "#/definitions/schedule.ScheduleTaskUpdateRequest"}}, "timezone": {"type": "string"}}}, "user.LoginVo": {"type": "object", "properties": {"gcm": {"type": "string", "example": "gcm_key"}, "headUrl": {"type": "string", "example": "https://example.com/avatar.jpg"}, "id": {"type": "integer", "example": 1}, "nickname": {"type": "string", "example": "昵称"}, "token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "username": {"type": "string", "example": "testuser"}}}, "user.rsaPublic": {"type": "object", "required": ["key", "public<PERSON>ey"], "properties": {"key": {"type": "string", "example": "random_key"}, "publicKey": {"type": "string", "example": "-----BEGIN PUBLIC KEY-----..."}}}, "user.userPlatformSecret": {"type": "object", "required": ["code", "key", "password", "username"], "properties": {"code": {"type": "string", "example": "FB"}, "key": {"type": "string", "example": "rsa_key"}, "password": {"type": "string", "example": "encrypted_password"}, "username": {"type": "string", "example": "platform_user"}}}, "user.userSecret": {"type": "object", "required": ["key", "password", "username"], "properties": {"key": {"type": "string", "example": "rsa_key"}, "password": {"type": "string", "example": "encrypted_password"}, "username": {"type": "string", "example": "testuser"}}}, "utils.Result": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {}, "msg": {"type": "string", "example": "success"}}}}}