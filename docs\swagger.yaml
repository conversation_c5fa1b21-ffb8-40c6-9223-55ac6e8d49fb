basePath: /api
definitions:
  ai.Content:
    properties:
      content:
        example: 验证码图片base64编码
        type: string
    type: object
  ai.Contents:
    properties:
      contents:
        example:
        - '[''你好'''
        - '''请介绍一下自己'']'
        items:
          type: string
        type: array
    type: object
  ai.TranscriptionRequest:
    properties:
      input:
        properties:
          url:
            example: https://example.com/audio.mp3
            type: string
        type: object
      model:
        example: whisper-1
        type: string
      parameters:
        properties:
          language:
            example: zh
            type: string
        type: object
    type: object
  cad.addCadModel:
    properties:
      cads:
        items:
          $ref: '#/definitions/cad.cadModel'
        type: array
    type: object
  cad.cadModel:
    properties:
      url:
        example: https://example.com/image.jpg
        type: string
      x:
        example: 100.5
        type: number
      "y":
        example: 200.3
        type: number
    type: object
  entity.CadEntity:
    properties:
      cad_id:
        description: 允许NULL的bigint
        type: integer
      id:
        type: integer
      url:
        description: 长文本URL
        type: string
      x:
        description: decimal(10,4)
        type: number
      "y":
        description: decimal(10,4)
        type: number
    type: object
  entity.CustomerGatherEntity:
    properties:
      gatherTime:
        description: 采集时间，允许NULL
        type: string
      gatherType:
        description: 采集类型，最大长度100
        type: string
      id:
        type: integer
      keyWord:
        type: string
      platformCode:
        description: 平台编码，最大长度100
        type: string
      platformId:
        description: 平台ID，允许NULL
        type: integer
      sameId:
        description: 关联ID，允许NULL
        type: integer
      userId:
        description: 用户ID，允许NULL
        type: integer
    type: object
  entity.TaskEntity:
    properties:
      category:
        type: string
      defaultConfig:
        type: string
      id:
        type: integer
      status:
        type: integer
      taskCode:
        type: string
      taskDescription:
        type: string
      taskName:
        type: string
      taskType:
        type: string
    type: object
  gather.AddGatherRequest:
    properties:
      gatherTime:
        description: 采集时间，允许NULL
        example: "2024-01-01T00:00:00Z"
        type: string
      gatherType:
        description: 采集类型，最大长度100
        example: 用户信息
        type: string
      keyWord:
        example: 关键词
        type: string
      platformCode:
        description: 平台编码，最大长度100
        example: FB
        type: string
      platformId:
        description: 平台ID，允许NULL
        example: 123456
        type: integer
      results:
        items:
          $ref: '#/definitions/gather.AddGatherResultRequest'
        type: array
    type: object
  gather.AddGatherResultRequest:
    properties:
      accountId:
        example: user123
        type: string
      accountName:
        example: 用户名
        type: string
      accountSynopsis:
        example: 用户简介
        type: string
      url:
        example: https://example.com/profile
        type: string
    type: object
  gather.GatherPageResult:
    properties:
      list:
        description: 数据列表
        items:
          $ref: '#/definitions/entity.CustomerGatherEntity'
        type: array
      page:
        description: 当前页码
        example: 1
        type: integer
      pageSize:
        description: 每页数量
        example: 10
        type: integer
      total:
        description: 总记录数
        example: 100
        type: integer
      totalPage:
        description: 总页数
        example: 10
        type: integer
    type: object
  ip.address:
    properties:
      address:
        example: 北京
        type: string
    required:
    - address
    type: object
  ipdata.IpData:
    properties:
      ip:
        example: ***********
        type: string
      port:
        example: "8080"
        type: string
    type: object
  job.WebBrowserStartRequest:
    properties:
      platformCode:
        example: FB
        type: string
      platformUserName:
        example: testuser
        type: string
    required:
    - platformCode
    - platformUserName
    type: object
  schedule.ScheduleCreateRequest:
    properties:
      config:
        example: '{}'
        type: string
      cpuLimit:
        example: 2
        type: number
      cronExpression:
        example: 0 0 2 * * ?
        type: string
      description:
        example: 每日数据同步调度
        type: string
      endTime:
        example: "2024-12-31T23:59:59Z"
        type: string
      failureNotify:
        example: '{"email":"<EMAIL>"}'
        type: string
      fallbackAction:
        example: STOP
        type: string
      holidayHandle:
        example: SKIP
        type: string
      intervalSeconds:
        example: 3600
        type: integer
      loadBalanceType:
        example: ROUND_ROBIN
        type: string
      maxConcurrency:
        example: 1
        type: integer
      maxRetryCount:
        example: 3
        type: integer
      memoryLimit:
        example: 1024
        type: integer
      queuePriority:
        example: 5
        type: integer
      retryInterval:
        example: 60
        type: integer
      scheduleName:
        example: 数据同步任务
        type: string
      scheduleType:
        example: CRON
        type: string
      startTime:
        example: "2024-01-01T00:00:00Z"
        type: string
      tasks:
        items:
          $ref: '#/definitions/schedule.ScheduleTaskCreateRequest'
        type: array
      timezone:
        example: Asia/Shanghai
        type: string
    required:
    - scheduleName
    - scheduleType
    type: object
  schedule.ScheduleListResponse:
    properties:
      schedules:
        items:
          $ref: '#/definitions/schedule.ScheduleResponse'
        type: array
      total:
        type: integer
    type: object
  schedule.ScheduleResponse:
    properties:
      config:
        type: string
      cpuLimit:
        type: number
      createTime:
        type: string
      cronExpression:
        type: string
      description:
        type: string
      endTime:
        type: string
      failureNotify:
        type: string
      fallbackAction:
        type: string
      holidayHandle:
        type: string
      id:
        type: integer
      intervalSeconds:
        type: integer
      loadBalanceType:
        type: string
      maxConcurrency:
        type: integer
      maxRetryCount:
        type: integer
      memoryLimit:
        type: integer
      queuePriority:
        type: integer
      retryInterval:
        type: integer
      scheduleName:
        type: string
      scheduleType:
        type: string
      startTime:
        type: string
      status:
        type: integer
      tasks:
        items:
          $ref: '#/definitions/schedule.ScheduleTaskResponse'
        type: array
      timezone:
        type: string
      updateTime:
        type: string
    type: object
  schedule.ScheduleTaskCreateRequest:
    properties:
      dependencyCondition:
        example: ""
        type: string
      dependencyType:
        example: SUCCESS
        type: string
      inputMapping:
        example: '{}'
        type: string
      outputMapping:
        example: '{}'
        type: string
      postTaskIds:
        example: ""
        type: string
      preTaskIds:
        example: ""
        type: string
      taskConfig:
        example: '{}'
        type: string
      taskId:
        example: 1
        type: integer
      taskOrder:
        example: 1
        type: integer
      timeoutSeconds:
        example: 3600
        type: integer
    required:
    - taskId
    - taskOrder
    type: object
  schedule.ScheduleTaskResponse:
    properties:
      createTime:
        type: string
      dependencyCondition:
        type: string
      dependencyType:
        type: string
      id:
        type: integer
      inputMapping:
        type: string
      outputMapping:
        type: string
      postTaskIds:
        type: string
      preTaskIds:
        type: string
      status:
        type: integer
      taskCode:
        type: string
      taskConfig:
        type: string
      taskId:
        type: integer
      taskName:
        type: string
      taskOrder:
        type: integer
      timeoutSeconds:
        type: integer
      updateTime:
        type: string
    type: object
  schedule.ScheduleTaskUpdateRequest:
    properties:
      dependencyCondition:
        type: string
      dependencyType:
        type: string
      id:
        type: integer
      inputMapping:
        type: string
      outputMapping:
        type: string
      postTaskIds:
        type: string
      preTaskIds:
        type: string
      status:
        type: integer
      taskConfig:
        type: string
      taskId:
        type: integer
      taskOrder:
        type: integer
      timeoutSeconds:
        type: integer
    required:
    - taskId
    - taskOrder
    type: object
  schedule.ScheduleUpdateRequest:
    properties:
      config:
        type: string
      cpuLimit:
        type: number
      cronExpression:
        type: string
      description:
        type: string
      endTime:
        type: string
      failureNotify:
        type: string
      fallbackAction:
        type: string
      holidayHandle:
        type: string
      id:
        type: integer
      intervalSeconds:
        type: integer
      loadBalanceType:
        type: string
      maxConcurrency:
        type: integer
      maxRetryCount:
        type: integer
      memoryLimit:
        type: integer
      queuePriority:
        type: integer
      retryInterval:
        type: integer
      scheduleName:
        type: string
      scheduleType:
        type: string
      startTime:
        type: string
      status:
        type: integer
      tasks:
        items:
          $ref: '#/definitions/schedule.ScheduleTaskUpdateRequest'
        type: array
      timezone:
        type: string
    required:
    - id
    - scheduleName
    - scheduleType
    type: object
  user.LoginVo:
    properties:
      gcm:
        example: gcm_key
        type: string
      headUrl:
        example: https://example.com/avatar.jpg
        type: string
      id:
        example: 1
        type: integer
      nickname:
        example: 昵称
        type: string
      token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
      username:
        example: testuser
        type: string
    type: object
  user.rsaPublic:
    properties:
      key:
        example: random_key
        type: string
      publicKey:
        example: '-----BEGIN PUBLIC KEY-----...'
        type: string
    required:
    - key
    - publicKey
    type: object
  user.userPlatformSecret:
    properties:
      code:
        example: FB
        type: string
      key:
        example: rsa_key
        type: string
      password:
        example: encrypted_password
        type: string
      username:
        example: platform_user
        type: string
    required:
    - code
    - key
    - password
    - username
    type: object
  user.userSecret:
    properties:
      key:
        example: rsa_key
        type: string
      password:
        example: encrypted_password
        type: string
      username:
        example: testuser
        type: string
    required:
    - key
    - password
    - username
    type: object
  utils.Result:
    properties:
      code:
        example: 200
        type: integer
      data: {}
      msg:
        example: success
        type: string
    type: object
host: localhost:8080
info:
  contact: {}
  description: Mozhao 服务端 API 文档
  title: Mozhao Server API
  version: "1.0"
paths:
  /api/ai/captchaRecognition:
    post:
      consumes:
      - application/json
      description: 使用AI识别验证码图片内容
      parameters:
      - description: 验证码图片内容
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/ai.Content'
      produces:
      - application/json
      responses:
        "200":
          description: 识别结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  type: string
              type: object
      summary: 验证码识别
      tags:
      - ai
  /api/ai/identify:
    post:
      consumes:
      - application/json
      description: 识别音频文件内容
      parameters:
      - description: 音频识别请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/ai.TranscriptionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 识别结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  type: string
              type: object
      summary: 音频识别
      tags:
      - ai
  /api/ai/textMessage:
    post:
      consumes:
      - application/json
      description: 与AI进行多轮文本对话
      parameters:
      - description: 对话内容列表
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/ai.Contents'
      produces:
      - application/json
      responses:
        "200":
          description: AI回复内容
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  type: string
              type: object
      summary: AI文本对话
      tags:
      - ai
  /api/cad/add:
    post:
      consumes:
      - application/json
      description: 批量添加CAD坐标点数据
      parameters:
      - description: CAD数据列表
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/cad.addCadModel'
      produces:
      - application/json
      responses:
        "200":
          description: 添加结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  type: boolean
              type: object
      summary: 添加CAD坐标数据
      tags:
      - cad
  /api/cad/query:
    get:
      consumes:
      - application/json
      description: 获取所有CAD坐标数据
      produces:
      - application/json
      responses:
        "200":
          description: CAD数据列表
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/entity.CadEntity'
                  type: array
              type: object
      summary: 查询CAD数据列表
      tags:
      - cad
  /api/gather/add:
    post:
      consumes:
      - application/json
      description: 新建一个网页内容采集任务
      parameters:
      - description: 采集任务信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/gather.AddGatherRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  type: boolean
              type: object
      security:
      - Bearer: []
      summary: 创建采集任务
      tags:
      - gather
  /api/gather/query:
    get:
      consumes:
      - application/json
      description: 分页查询采集任务列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页数量
        in: query
        name: pageSize
        required: true
        type: integer
      - description: 采集类型
        in: query
        name: gatherType
        type: string
      - description: 关键词
        in: query
        name: keyWord
        type: string
      - description: 平台编码
        in: query
        name: platformCode
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 分页查询结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  $ref: '#/definitions/gather.GatherPageResult'
              type: object
      security:
      - Bearer: []
      summary: 查询采集列表
      tags:
      - gather
  /api/gather/queryInfo:
    get:
      consumes:
      - application/json
      description: 根据ID获取采集任务详细信息
      parameters:
      - description: 采集任务ID
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 采集详情，包含gather和result字段
          schema:
            $ref: '#/definitions/utils.Result'
      security:
      - Bearer: []
      summary: 获取采集详情
      tags:
      - gather
  /api/ip/getIp:
    post:
      consumes:
      - application/json
      description: 根据地址查询可用的IP代理信息
      parameters:
      - description: 地址信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/ip.address'
      produces:
      - application/json
      responses:
        "200":
          description: IP代理信息
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  $ref: '#/definitions/ipdata.IpData'
              type: object
      summary: 查询IP代理信息
      tags:
      - ip
  /api/job/browserStart:
    post:
      consumes:
      - application/json
      description: 为指定平台用户启动浏览器会话
      parameters:
      - description: 浏览器启动请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/job.WebBrowserStartRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 启动结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  type: boolean
              type: object
      security:
      - Bearer: []
      summary: 启动浏览器任务
      tags:
      - job
  /api/schedule/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定的任务调度配置
      parameters:
      - description: 调度ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  type: boolean
              type: object
      security:
      - Bearer: []
      summary: 删除调度
      tags:
      - schedule
    get:
      consumes:
      - application/json
      description: 根据ID获取调度的详细信息
      parameters:
      - description: 调度ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 调度详情
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  $ref: '#/definitions/schedule.ScheduleResponse'
              type: object
      security:
      - Bearer: []
      summary: 获取调度详情
      tags:
      - schedule
  /api/schedule/{id}/status:
    put:
      consumes:
      - application/json
      description: 启用、禁用或暂停调度
      parameters:
      - description: 调度ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      - description: 状态更新请求
        in: body
        name: body
        required: true
        schema:
          properties:
            status:
              type: integer
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: 更新结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  type: boolean
              type: object
      security:
      - Bearer: []
      summary: 更新调度状态
      tags:
      - schedule
  /api/schedule/create:
    post:
      consumes:
      - application/json
      description: 创建新的任务调度配置
      parameters:
      - description: 调度创建请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/schedule.ScheduleCreateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功返回调度ID
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  format: int64
                  type: integer
              type: object
      security:
      - Bearer: []
      summary: 创建调度
      tags:
      - schedule
  /api/schedule/list:
    get:
      consumes:
      - application/json
      description: 获取当前用户的所有调度配置列表
      produces:
      - application/json
      responses:
        "200":
          description: 调度列表
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  $ref: '#/definitions/schedule.ScheduleListResponse'
              type: object
      security:
      - Bearer: []
      summary: 获取用户所有调度
      tags:
      - schedule
  /api/schedule/tasks:
    get:
      consumes:
      - application/json
      description: 获取系统中所有可用的任务类型
      produces:
      - application/json
      responses:
        "200":
          description: 任务列表
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/entity.TaskEntity'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 获取所有可用任务
      tags:
      - schedule
  /api/schedule/update:
    put:
      consumes:
      - application/json
      description: 更新现有的任务调度配置
      parameters:
      - description: 调度更新请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/schedule.ScheduleUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  type: boolean
              type: object
      security:
      - Bearer: []
      summary: 更新调度
      tags:
      - schedule
  /api/user/addPlatform:
    post:
      consumes:
      - application/json
      description: 为用户添加第三方平台账号信息
      parameters:
      - description: 加密的平台账号信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/user.userPlatformSecret'
      produces:
      - application/json
      responses:
        "200":
          description: 添加结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  type: boolean
              type: object
      security:
      - Bearer: []
      summary: 添加平台账号
      tags:
      - user
  /api/user/getPublicKey:
    post:
      consumes:
      - application/json
      description: 获取用于加密的RSA公钥
      produces:
      - application/json
      responses:
        "200":
          description: RSA公钥信息
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  $ref: '#/definitions/user.rsaPublic'
              type: object
      summary: 获取RSA公钥
      tags:
      - user
  /api/user/login:
    post:
      consumes:
      - application/json
      description: 通过加密账号密码登录
      parameters:
      - description: 加密的用户登录信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/user.userSecret'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功返回用户信息和token
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  $ref: '#/definitions/user.LoginVo'
              type: object
      summary: 登录
      tags:
      - user
  /api/user/register:
    post:
      consumes:
      - application/json
      description: 通过加密账号密码注册
      parameters:
      - description: 加密的用户注册信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/user.userSecret'
      produces:
      - application/json
      responses:
        "200":
          description: 注册结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Result'
            - properties:
                data:
                  type: boolean
              type: object
      summary: 注册
      tags:
      - user
swagger: "2.0"
