package entity

import "time"

// SearchResultDataEntity 搜索结果数据实体
type SearchResultDataEntity struct {
	BaseEntity
	UserId             int64     `gorm:"column:user_id;type:bigint;not null;index;comment:用户ID" json:"userId"`
	SearchResultDataID string    `gorm:"column:search_result_data_id;type:varchar(255);comment:原始搜索结果ID" json:"searchResultDataId"`
	Name               string    `gorm:"column:name;type:varchar(255);comment:名称" json:"name"`
	Username           string    `gorm:"column:username;type:varchar(255);comment:用户名" json:"username,omitempty"`
	ProfileUrl         string    `gorm:"column:profile_url;type:varchar(500);comment:档案URL" json:"profileUrl"`
	Avatar             string    `gorm:"column:avatar;type:varchar(500);comment:头像URL" json:"avatar,omitempty"`
	Description        string    `gorm:"column:description;type:text;comment:描述" json:"description,omitempty"`
	Snippet            string    `gorm:"column:snippet;type:text;comment:摘要" json:"snippet,omitempty"`
	FollowerCount      int64     `gorm:"column:follower_count;type:bigint;default:0;comment:粉丝数" json:"followerCount,omitempty"`
	Keyword            string    `gorm:"column:keyword;type:varchar(255);comment:搜索关键词" json:"keyword"`
	Platform           string    `gorm:"column:platform;type:varchar(50);comment:平台" json:"platform"`
	Rank               int       `gorm:"column:rank;type:int;comment:排名" json:"rank,omitempty"`
	CollectedAt        time.Time `gorm:"column:collected_at;type:datetime;comment:采集时间" json:"collectedAt"`
}

// TableName 指定表名
func (SearchResultDataEntity) TableName() string {
	return "search_result_data"
}
