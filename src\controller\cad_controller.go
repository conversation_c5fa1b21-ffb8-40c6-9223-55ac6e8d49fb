package controller

import (
	cadService "mozhao/src/service/cad"

	"github.com/gin-gonic/gin"
)

func CadController(rootGroup *gin.RouterGroup) {
	userGroup := rootGroup.Group("/cad")
	userGroup.POST("/add", addCad)
	userGroup.GET("/query", queryCad)
}

// addCad 添加CAD数据
// @Summary	添加CAD坐标数据
// @Description 批量添加CAD坐标点数据
// @Tags	cad
// @Accept	json
// @Produce	json
// @Param	body body cad.addCadModel true "CAD数据列表"
// @Success	200 {object} utils.Result{data=bool} "添加结果"
// @Router	/api/cad/add [post]
func addCad(context *gin.Context) {
	Handler(context, cadService.Add)
}

// queryCad 查询CAD数据
// @Summary	查询CAD数据列表
// @Description 获取所有CAD坐标数据
// @Tags	cad
// @Accept	json
// @Produce	json
// @Success	200 {object} utils.Result{data=[]entity.CadEntity} "CAD数据列表"
// @Router	/api/cad/query [get]
func queryCad(context *gin.Context) {
	Handler(context, cadService.Query)
}
