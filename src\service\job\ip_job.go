package job

import (
	"io"
	"mozhao/src/common/ipdata"
	"net"
	"net/http"
	"net/url"
	"regexp"
	"sync"
	"time"
)

func generateUrl(address string) string {
	return "https://www.kuaidaili.com/free/inha/" + address + "/"
}

func checkProxy(proxy string) bool {
	client := &http.Client{
		Timeout: 5 * time.Second,
		Transport: &http.Transport{
			Proxy: http.ProxyURL(&url.URL{
				Scheme: "http",
				Host:   proxy,
			}),
		},
	}

	resp, err := client.Get("http://httpbin.org/ip")
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == 200
}

func FindIp(address string) {
	// 获取网页内容
	content, err := fetchPage(generateUrl(address))
	if err != nil {
		return
	}

	// 提取IP地址
	ipPorts := extractIPs(content)

	// 创建管道
	taskChan := make(chan string, len(ipPorts))
	resultChan := make(chan string)

	// 启动worker池
	const workers = 20
	var wg sync.WaitGroup
	wg.Add(workers)

	for i := 0; i < workers; i++ {
		go worker(taskChan, resultChan, &wg)
	}

	// 分发任务
	go func() {
		for _, ipPort := range ipPorts {
			taskChan <- ipPort
		}
		close(taskChan)
	}()

	// 收集结果
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	var ips = []ipdata.IpData{}
	for proxy := range resultChan {
		ip, post, _ := net.SplitHostPort(proxy)
		ips = append(ips, ipdata.IpData{Ip: ip, Port: post})
	}
	ipdata.IpDataMap[address] = ips
}

func fetchPage(url string) (string, error) {
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Get(url)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	return string(body), nil
}

func extractIPs(content string) []string {
	re := regexp.MustCompile(`\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d+`)
	return re.FindAllString(content, -1)
}

func worker(taskChan <-chan string, resultChan chan<- string, wg *sync.WaitGroup) {
	defer wg.Done()
	for ipPort := range taskChan {
		if checkProxy(ipPort) {
			resultChan <- ipPort
		}
	}
}
