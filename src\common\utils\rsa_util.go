package utils

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/pem"
	"errors"
	"fmt"
)

// GenerateRSAKeyPair 生成RSA密钥对（2048位）
func GenerateRSAKeyPair() (*rsa.PrivateKey, *rsa.PublicKey, error) {
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return nil, nil, err
	}
	return privateKey, &privateKey.PublicKey, nil
}

// ExportPrivateKeyAsPEMStr 将RSA私钥转换为PEM字符串
func ExportPrivateKeyAsPEMStr(privateKey *rsa.PrivateKey) (string, error) {
	privateKeyBytes, err := x509.MarshalPKCS8PrivateKey(privateKey)
	if err != nil {
		return "", err
	}
	privateKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PRIVATE KEY",
		Bytes: privateKeyBytes,
	})
	return string(privateKeyPEM), nil
}

// ExportPublicKeyAsPEMStr 将RSA公钥转换为PEM字符串
func ExportPublicKeyAsPEMStr(publicKey *rsa.PublicKey) (string, error) {
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(publicKey)
	if err != nil {
		return "", err
	}
	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyBytes,
	})
	return string(publicKeyPEM), nil
}

// ParsePrivateKeyFromPEMStr 从PEM字符串解析RSA私钥
func ParsePrivateKeyFromPEMStr(privateKeyPEM string) (*rsa.PrivateKey, error) {
	block, _ := pem.Decode([]byte(privateKeyPEM))
	if block == nil {
		return nil, errors.New("failed to parse PEM block containing the private key")
	}

	privateKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}

	rsaPrivateKey, ok := privateKey.(*rsa.PrivateKey)
	if !ok {
		return nil, errors.New("not an RSA private key")
	}

	return rsaPrivateKey, nil
}

// ParsePublicKeyFromPEMStr 从PEM字符串解析RSA公钥
func ParsePublicKeyFromPEMStr(publicKeyPEM string) (*rsa.PublicKey, error) {
	block, _ := pem.Decode([]byte(publicKeyPEM))
	if block == nil {
		return nil, errors.New("failed to parse PEM block containing the public key")
	}

	publicKey, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, err
	}

	rsaPublicKey, ok := publicKey.(*rsa.PublicKey)
	if !ok {
		return nil, errors.New("not an RSA public key")
	}

	return rsaPublicKey, nil
}

// RSAEncrypt 使用RSA公钥加密数据
func RSAEncrypt(publicKey *rsa.PublicKey, plaintext []byte) ([]byte, error) {
	return rsa.EncryptOAEP(sha256.New(), rand.Reader, publicKey, plaintext, nil)
}

// RSADecrypt 使用RSA私钥解密数据
func RSADecrypt(privateKey *rsa.PrivateKey, ciphertext []byte) ([]byte, error) {
	return rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, ciphertext, nil)
}

// RSASign 使用RSA私钥对数据进行签名
func RSASign(privateKey *rsa.PrivateKey, data []byte) ([]byte, error) {
	hashed := sha256.Sum256(data)
	return rsa.SignPKCS1v15(rand.Reader, privateKey, 0, hashed[:])
}

// RSAVerify 使用RSA公钥验证签名
func RSAVerify(publicKey *rsa.PublicKey, data []byte, signature []byte) error {
	hashed := sha256.Sum256(data)
	return rsa.VerifyPKCS1v15(publicKey, 0, hashed[:], signature)
}

// 示例用法
func ExampleUsage() {
	// 生成密钥对
	privateKey, publicKey, err := GenerateRSAKeyPair()
	if err != nil {
		fmt.Println("Error generating key pair:", err)
		return
	}

	// 导出为PEM字符串
	privateKeyPEM, _ := ExportPrivateKeyAsPEMStr(privateKey)
	publicKeyPEM, _ := ExportPublicKeyAsPEMStr(publicKey)

	fmt.Println("Private Key PEM:")
	fmt.Println(privateKeyPEM)
	fmt.Println("Public Key PEM:")
	fmt.Println(publicKeyPEM)

	// 从PEM字符串解析密钥
	parsedPrivateKey, _ := ParsePrivateKeyFromPEMStr(privateKeyPEM)
	parsedPublicKey, _ := ParsePublicKeyFromPEMStr(publicKeyPEM)

	// 加密和解密
	plaintext := []byte("Hello, RSA!")
	ciphertext, _ := RSAEncrypt(parsedPublicKey, plaintext)
	decrypted, _ := RSADecrypt(parsedPrivateKey, ciphertext)

	fmt.Printf("Original: %s\n", plaintext)
	fmt.Printf("Decrypted: %s\n", decrypted)

	// 签名和验证
	signature, _ := RSASign(parsedPrivateKey, plaintext)
	err = RSAVerify(parsedPublicKey, plaintext, signature)
	if err != nil {
		fmt.Println("Signature verification failed:", err)
	} else {
		fmt.Println("Signature verified successfully!")
	}
}
