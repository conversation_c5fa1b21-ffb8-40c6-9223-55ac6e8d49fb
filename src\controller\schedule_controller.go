package controller

import (
	"mozhao/src/service"
	scheduleService "mozhao/src/service/schedule"

	"github.com/gin-gonic/gin"
)

func ScheduleController(rootGroup *gin.RouterGroup) {
	// 需要权限检查的接口
	scheduleGroup := rootGroup.Group("/schedule", service.LoginCheck)
	
	// 调度管理接口
	scheduleGroup.POST("/create", createSchedule)
	scheduleGroup.PUT("/update", updateSchedule)
	scheduleGroup.DELETE("/:id", deleteSchedule)
	scheduleGroup.GET("/:id", getScheduleById)
	scheduleGroup.GET("/list", getUserSchedules)
	scheduleGroup.PUT("/:id/status", updateScheduleStatus)
	
	// 任务管理接口
	scheduleGroup.GET("/tasks", getAllTasks)
}

// createSchedule 创建调度
// @Summary      创建调度
// @Description  创建新的任务调度配置
// @Tags         schedule
// @Accept       json
// @Produce      json
// @Param        body body schedule.ScheduleCreateRequest true "调度创建请求"
// @Success      200  {object}  utils.Result{data=int64} "创建成功返回调度ID"
// @Security     Bearer
// @Router       /api/schedule/create [post]
func createSchedule(context *gin.Context) {
	Handler(context, scheduleService.CreateSchedule)
}

// updateSchedule 更新调度
// @Summary      更新调度
// @Description  更新现有的任务调度配置
// @Tags         schedule
// @Accept       json
// @Produce      json
// @Param        body body schedule.ScheduleUpdateRequest true "调度更新请求"
// @Success      200  {object}  utils.Result{data=bool} "更新结果"
// @Security     Bearer
// @Router       /api/schedule/update [put]
func updateSchedule(context *gin.Context) {
	Handler(context, scheduleService.UpdateSchedule)
}

// deleteSchedule 删除调度
// @Summary      删除调度
// @Description  删除指定的任务调度配置
// @Tags         schedule
// @Accept       json
// @Produce      json
// @Param        id   path      int64  true  "调度ID"
// @Success      200  {object}  utils.Result{data=bool} "删除结果"
// @Security     Bearer
// @Router       /api/schedule/{id} [delete]
func deleteSchedule(context *gin.Context) {
	Handler(context, scheduleService.DeleteSchedule)
}

// getScheduleById 获取调度详情
// @Summary      获取调度详情
// @Description  根据ID获取调度的详细信息
// @Tags         schedule
// @Accept       json
// @Produce      json
// @Param        id   path      int64  true  "调度ID"
// @Success      200  {object}  utils.Result{data=schedule.ScheduleResponse} "调度详情"
// @Security     Bearer
// @Router       /api/schedule/{id} [get]
func getScheduleById(context *gin.Context) {
	Handler(context, scheduleService.GetScheduleById)
}

// getUserSchedules 获取用户所有调度
// @Summary      获取用户所有调度
// @Description  获取当前用户的所有调度配置列表
// @Tags         schedule
// @Accept       json
// @Produce      json
// @Success      200  {object}  utils.Result{data=schedule.ScheduleListResponse} "调度列表"
// @Security     Bearer
// @Router       /api/schedule/list [get]
func getUserSchedules(context *gin.Context) {
	Handler(context, scheduleService.GetUserSchedules)
}

// updateScheduleStatus 更新调度状态
// @Summary      更新调度状态
// @Description  启用、禁用或暂停调度
// @Tags         schedule
// @Accept       json
// @Produce      json
// @Param        id   path      int64  true  "调度ID"
// @Param        body body      object{status=int} true "状态更新请求"
// @Success      200  {object}  utils.Result{data=bool} "更新结果"
// @Security     Bearer
// @Router       /api/schedule/{id}/status [put]
func updateScheduleStatus(context *gin.Context) {
	Handler(context, scheduleService.UpdateScheduleStatus)
}

// getAllTasks 获取所有可用任务
// @Summary      获取所有可用任务
// @Description  获取系统中所有可用的任务类型
// @Tags         schedule
// @Accept       json
// @Produce      json
// @Success      200  {object}  utils.Result{data=[]entity.TaskEntity} "任务列表"
// @Security     Bearer
// @Router       /api/schedule/tasks [get]
func getAllTasks(context *gin.Context) {
	Handler(context, scheduleService.GetAllTasks)
}
