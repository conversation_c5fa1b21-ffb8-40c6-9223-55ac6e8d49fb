package controller

import (
	"mozhao/src/service"
	jobService "mozhao/src/service/job"

	"github.com/gin-gonic/gin"
)

func JobController(rootGroup *gin.RouterGroup) {
	jobGroup := rootGroup.Group("/job", service.LoginCheck)
	jobGroup.POST("/browserStart", browserStart)
}

// browserStart 启动浏览器任务
// @Summary	启动浏览器任务
// @Description 为指定平台用户启动浏览器会话
// @Tags	job
// @Accept	json
// @Produce	json
// @Param	body body job.WebBrowserStartRequest true "浏览器启动请求"
// @Success	200 {object} utils.Result{data=bool} "启动结果"
// @Security	Bearer
// @Router	/api/job/browserStart [post]
func browserStart(context *gin.Context) {
	Handler(context, jobService.WebBrowserStart)
}
