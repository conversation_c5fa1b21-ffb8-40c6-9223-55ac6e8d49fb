package user

import (
	"encoding/base64"
	"errors"
	"mozhao/src/common/consts"
	"mozhao/src/common/mysql"
	"mozhao/src/common/rdb"
	"mozhao/src/common/utils"
	"mozhao/src/model/entity"
	"mozhao/src/model/vo/user"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/redis/go-redis/v9"
	"golang.org/x/crypto/bcrypt"
)

type userSecret struct {
	Username string `json:"username" binding:"required" example:"testuser"`
	Password string `json:"password" binding:"required" example:"encrypted_password"`
	Key      string `json:"key" binding:"required" example:"rsa_key"`
}

type rsaPublic struct {
	PublicKey string `json:"publicKey" binding:"required" example:"-----BEGIN PUBLIC KEY-----..."`
	Key       string `json:"key" binding:"required" example:"random_key"`
}

type userPlatformSecret struct {
	Username string `json:"username" binding:"required" example:"platform_user"`
	Password string `json:"password" binding:"required" example:"encrypted_password"`
	Key      string `json:"key" binding:"required" example:"rsa_key"`
	Code     string `json:"code" binding:"required" example:"FB"`
}

func Login(context *gin.Context) (any, error) {
	var result user.LoginVo
	var user entity.UserEntity
	username, password, err := getUserSecret(context)
	if err != nil {
		return false, errors.New(consts.ParamError)
	}
	mysql.Db.Where("username = ?", string(username)).Find(&user)
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), password)
	if err != nil {
		return &result, errors.New(consts.PasswordError)
	}
	copier.Copy(&result, &user)
	result.Token = utils.GenerateToken(user.Id)
	gcmKey, _ := utils.GenerateSecureRandomString(32)
	rdb.RedisClient.Set(rdb.RedisCtx, strconv.FormatInt(user.Id, 10), gcmKey, 24*7*time.Hour)
	result.Gcm = gcmKey
	return &result, nil
}

func Register(context *gin.Context) (any, error) {
	username, password, err := getUserSecret(context)
	if err != nil {
		return false, errors.New(consts.ParamError)
	}
	user := &entity.UserEntity{
		Username: string(username),
		Password: string(password),
	}
	var dbUser entity.UserEntity
	mysql.Db.Where("username = ?", user.Username).First(&dbUser)
	if dbUser.Id != 0 {
		return false, errors.New(consts.ExistError)
	}
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.Password), bcrypt.DefaultCost)
	user.Password = string(hashedPassword)
	mysql.Db.Create(&user)
	return false, nil
}

func GetPublicKey(context *gin.Context) (any, error) {
	privateKey, publicKey, err := utils.GenerateRSAKeyPair()
	if err != nil {
		return nil, err
	}
	key, _ := utils.GenerateSecureRandomString(31)
	publicKeyStr, _ := utils.ExportPublicKeyAsPEMStr(publicKey)
	privateKeyStr, _ := utils.ExportPrivateKeyAsPEMStr(privateKey)
	err = rdb.RedisClient.Set(rdb.RedisCtx, key, privateKeyStr, 5*time.Minute).Err()
	if err != nil {
		panic(err)
	}
	result := rsaPublic{
		PublicKey: publicKeyStr,
		Key:       key,
	}
	return result, nil
}

func getUserSecret(context *gin.Context) ([]byte, []byte, error) {
	var secret userSecret
	context.ShouldBind(&secret)
	return decodeSecret(secret.Key, secret.Username, secret.Password)
}

func decodeSecret(key string, username string, password string) ([]byte, []byte, error) {
	privateKeyStr, err := rdb.RedisClient.Get(rdb.RedisCtx, key).Result()
	if err == redis.Nil {
		return nil, nil, errors.New(consts.NotExistError)
	}
	privateKey, err := utils.ParsePrivateKeyFromPEMStr(privateKeyStr)
	if err != nil {
		return nil, nil, errors.New(consts.ParamError)
	}
	usernameByte, err := base64.StdEncoding.DecodeString(username)
	passwordByte, err := base64.StdEncoding.DecodeString(password)
	dUsername, _ := utils.RSADecrypt(privateKey, usernameByte)
	dPassword, _ := utils.RSADecrypt(privateKey, passwordByte)
	return dUsername, dPassword, nil
}

func AddPlatform(context *gin.Context) (any, error) {
	var userSecret userPlatformSecret
	context.ShouldBind(&userSecret)
	username, password, _ := decodeSecret(userSecret.Key, userSecret.Username, userSecret.Password)
	userId := utils.GetUser(context)
	platform := entity.UserPlatformEntity{
		PlatformCode:     userSecret.Code,
		PlatformPassword: string(password),
		PlatformUsername: string(username),
		UserId:           userId,
	}
	platform.PlatformEncode()
	mysql.Db.Create(&platform)
	return true, nil
}

func MaxConn() int {
	return 5
}
