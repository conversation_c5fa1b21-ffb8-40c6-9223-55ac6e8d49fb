package timer

import (
	"context"
	"sync"
	"time"
)

// 定时任务管理器
type Timer struct {
	wg     sync.WaitGroup
	ctx    context.Context
	cancel context.CancelFunc
}

func NewTimer() *Timer {
	ctx, cancel := context.WithCancel(context.Background())
	return &Timer{
		ctx:    ctx,
		cancel: cancel,
	}
}

// 注册定时任务
func (t *Timer) AddJob(interval time.Duration, job func()) {
	t.wg.Add(1)
	go func() {
		defer t.wg.Done()
		ticker := time.NewTicker(interval)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				job()
			case <-t.ctx.Done():
				return
			}
		}
	}()
}

// 停止所有任务
func (t *Timer) Stop() {
	t.cancel()
	t.wg.Wait()
}
