package controller

import (
	"mozhao/src/service"
	"mozhao/src/service/gather"

	"github.com/gin-gonic/gin"
)

func GatherController(rootGroup *gin.RouterGroup) {
	gatherGroup := rootGroup.Group("/gather", service.FullCheck)
	gatherGroup.POST("/add", addGather)
	gatherGroup.GET("/query", queryGather)
	gatherGroup.GET("/queryInfo", queryGatherInfo)
}

// addGather 新建采集任务
// @Summary	创建采集任务
// @Description 新建一个网页内容采集任务
// @Tags	gather
// @Accept	json
// @Produce	json
// @Param	body body gather.AddGatherRequest true "采集任务信息"
// @Success	200 {object} utils.Result{data=bool} "创建结果"
// @Security	Bearer
// @Router	/api/gather/add [post]
func addGather(ctx *gin.Context) {
	Handler(ctx, gather.AddGather)
}

// queryGather 查询采集任务
// @Summary	查询采集列表
// @Description 分页查询采集任务列表
// @Tags	gather
// @Accept	json
// @Produce	json
// @Param	page query int true "页码"
// @Param	pageSize query int true "每页数量"
// @Param	gatherType query string false "采集类型"
// @Param	keyWord query string false "关键词"
// @Param	platformCode query string false "平台编码"
// @Success	200 {object} utils.Result{data=gather.GatherPageResult} "分页查询结果"
// @Security	Bearer
// @Router	/api/gather/query [get]
func queryGather(ctx *gin.Context) {
	Handler(ctx, gather.QueryGather)
}

// queryGatherInfo 查询采集详情
// @Summary	获取采集详情
// @Description 根据ID获取采集任务详细信息
// @Tags	gather
// @Accept	json
// @Produce	json
// @Param	id query string true "采集任务ID"
// @Success	200 {object} utils.Result "采集详情，包含gather和result字段"
// @Security	Bearer
// @Router	/api/gather/queryInfo [get]
func queryGatherInfo(ctx *gin.Context) {
	Handler(ctx, gather.QueryGatherInfo)
}
