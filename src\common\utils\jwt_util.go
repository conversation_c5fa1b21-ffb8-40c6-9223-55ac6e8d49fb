package utils

import (
	"github.com/golang-jwt/jwt/v5"
	"mozhao/src/config"
	"time"
)

var configData = config.ConfigData
var key = configData.Jwt.Key

func GenerateToken(userId int64) string {
	mapClaims := jwt.MapClaims{
		"userId": userId,
		"iat":    time.Now().Unix(),
		"exp":    time.Now().Add(24 * 7 * time.Hour).Unix(),
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, mapClaims)
	result, err := token.SignedString([]byte(key))
	if err != nil {
		panic(err)
	}
	return result
}

func ParseToken(token string) (jwt.MapClaims, error) {
	parsedToke, err := jwt.Parse(token, func(token *jwt.Token) (interface{}, error) {
		return []byte(key), nil
	})
	if err != nil {
		return nil, err
	}
	return parsedToke.Claims.(jwt.MapClaims), err
}
