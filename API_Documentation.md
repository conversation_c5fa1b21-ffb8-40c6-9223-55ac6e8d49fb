# Mozhao Server API 文档

## 基本信息

- **版本**: 1.0
- **基础路径**: `/api`
- **服务器地址**: `localhost:8080`
- **Swagger UI**: `http://localhost:8080/swagger/index.html`

## 接口概览

### 🤖 AI 模块 (ai)

#### 1. AI文本对话
- **接口**: `POST /api/ai/textMessage`
- **描述**: 与AI进行多轮文本对话
- **请求体**:
```json
{
  "contents": ["你好", "请介绍一下自己"]
}
```
- **响应**:
```json
{
  "code": 200,
  "msg": "success",
  "data": "AI回复内容"
}
```

#### 2. 验证码识别
- **接口**: `POST /api/ai/captchaRecognition`
- **描述**: 使用AI识别验证码图片内容
- **请求体**:
```json
{
  "content": "验证码图片base64编码"
}
```
- **响应**:
```json
{
  "code": 200,
  "msg": "success",
  "data": "识别结果"
}
```

#### 3. 音频识别
- **接口**: `POST /api/ai/identify`
- **描述**: 识别音频文件内容
- **请求体**:
```json
{
  "model": "whisper-1",
  "input": {
    "url": "https://example.com/audio.mp3"
  },
  "parameters": {
    "language": "zh"
  }
}
```

### 📐 CAD 模块 (cad)

#### 1. 添加CAD坐标数据
- **接口**: `POST /api/cad/add`
- **描述**: 批量添加CAD坐标点数据
- **请求体**:
```json
{
  "cads": [
    {
      "url": "https://example.com/image.jpg",
      "x": 100.5,
      "y": 200.3
    }
  ]
}
```

#### 2. 查询CAD数据列表
- **接口**: `GET /api/cad/query`
- **描述**: 获取所有CAD坐标数据
- **响应**:
```json
{
  "code": 200,
  "msg": "success",
  "data": [
    {
      "id": 1,
      "cad_id": 123456,
      "x": 100.5,
      "y": 200.3,
      "url": "https://example.com/image.jpg"
    }
  ]
}
```

### 👤 用户模块 (user)

#### 1. 用户登录
- **接口**: `POST /api/user/login`
- **描述**: 通过加密账号密码登录
- **请求体**:
```json
{
  "username": "testuser",
  "password": "encrypted_password",
  "key": "rsa_key"
}
```
- **响应**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "id": 1,
    "username": "testuser",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "nickname": "昵称",
    "headUrl": "https://example.com/avatar.jpg",
    "gcm": "gcm_key"
  }
}
```

#### 2. 用户注册
- **接口**: `POST /api/user/register`
- **描述**: 通过加密账号密码注册
- **请求体**: 同登录接口

#### 3. 获取RSA公钥
- **接口**: `POST /api/user/getPublicKey`
- **描述**: 获取用于加密的RSA公钥
- **响应**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "publicKey": "-----BEGIN PUBLIC KEY-----...",
    "key": "random_key"
  }
}
```

#### 4. 添加平台账号
- **接口**: `POST /api/user/addPlatform`
- **描述**: 为用户添加第三方平台账号信息
- **认证**: 需要Bearer Token
- **请求体**:
```json
{
  "username": "platform_user",
  "password": "encrypted_password",
  "key": "rsa_key",
  "code": "FB"
}
```

### 📊 数据采集模块 (gather)

#### 1. 创建采集任务
- **接口**: `POST /api/gather/add`
- **描述**: 新建一个网页内容采集任务
- **认证**: 需要Bearer Token
- **请求体**:
```json
{
  "platformId": 123456,
  "platformCode": "FB",
  "gatherTime": "2024-01-01T00:00:00Z",
  "gatherType": "用户信息",
  "keyWord": "关键词",
  "results": [
    {
      "url": "https://example.com/profile",
      "accountId": "user123",
      "accountName": "用户名",
      "accountSynopsis": "用户简介"
    }
  ]
}
```

#### 2. 查询采集列表
- **接口**: `GET /api/gather/query`
- **描述**: 分页查询采集任务列表
- **认证**: 需要Bearer Token
- **查询参数**:
  - `page` (int): 页码
  - `pageSize` (int): 每页数量
  - `gatherType` (string, 可选): 采集类型
  - `keyWord` (string, 可选): 关键词
  - `platformCode` (string, 可选): 平台编码

#### 3. 获取采集详情
- **接口**: `GET /api/gather/queryInfo`
- **描述**: 根据ID获取采集任务详细信息
- **认证**: 需要Bearer Token
- **查询参数**:
  - `id` (string): 采集任务ID

### 🌐 IP代理模块 (ip)

#### 1. 查询IP代理信息
- **接口**: `POST /api/ip/getIp`
- **描述**: 根据地址查询可用的IP代理信息
- **请求体**:
```json
{
  "address": "北京"
}
```
- **响应**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "ip": "***********",
    "port": "8080"
  }
}
```

### 🔧 任务管理模块 (job)

#### 1. 启动浏览器任务
- **接口**: `POST /api/job/browserStart`
- **描述**: 为指定平台用户启动浏览器会话
- **认证**: 需要Bearer Token
- **请求体**:
```json
{
  "platformCode": "FB",
  "platformUserName": "testuser"
}
```

## 通用响应格式

所有接口都遵循统一的响应格式：

```json
{
  "code": 200,
  "msg": "success",
  "data": "具体数据内容"
}
```

### 状态码说明
- `200`: 成功
- `400`: 请求错误
- `401`: 未授权
- `500`: 服务器错误

## 认证说明

部分接口需要Bearer Token认证，在请求头中添加：
```
Authorization: Bearer <your_token>
```

## 分页响应格式

分页接口的响应格式：
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "page": 1,
    "pageSize": 10,
    "total": 100,
    "totalPage": 10,
    "list": []
  }
}
```

## 错误处理

当请求失败时，响应格式为：
```json
{
  "code": 400,
  "msg": "错误信息描述",
  "data": null
}
```

## 开发说明

1. 所有接口都支持CORS跨域请求
2. 请求和响应的Content-Type为`application/json`
3. 敏感数据（如密码）需要使用RSA加密传输
4. 建议使用HTTPS协议进行生产环境部署
