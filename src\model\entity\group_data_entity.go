package entity

import "time"

// GroupDataEntity 群组社区数据实体
type GroupDataEntity struct {
	BaseEntity
	UserId      int64     `gorm:"column:user_id;type:bigint;not null;index;comment:用户ID" json:"userId"`
	GroupDataID string    `gorm:"column:group_data_id;type:varchar(255);comment:原始群组ID" json:"groupDataId"`
	Name        string    `gorm:"column:name;type:varchar(255);comment:群组名称" json:"name"`
	Description string    `gorm:"column:description;type:text;comment:群组描述" json:"description,omitempty"`
	MemberCount int64     `gorm:"column:member_count;type:bigint;default:0;comment:成员数" json:"memberCount,omitempty"`
	IsPrivate   bool      `gorm:"column:is_private;type:boolean;default:false;comment:是否私有" json:"isPrivate,omitempty"`
	Category    string    `gorm:"column:category;type:varchar(100);comment:分类" json:"category,omitempty"`
	AdminIds    string    `gorm:"column:admin_ids;type:json;comment:管理员ID列表" json:"adminIds,omitempty"`
	Rules       string    `gorm:"column:rules;type:json;comment:群组规则" json:"rules,omitempty"`
	GroupUrl    string    `gorm:"column:group_url;type:varchar(500);comment:群组URL" json:"groupUrl,omitempty"`
	Platform    string    `gorm:"column:platform;type:varchar(50);comment:平台" json:"platform"`
	CollectedAt time.Time `gorm:"column:collected_at;type:datetime;comment:采集时间" json:"collectedAt"`
}

// TableName 指定表名
func (GroupDataEntity) TableName() string {
	return "group_data"
}
