package config

import (
	_ "embed"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/spf13/viper"
)

// 嵌入环境配置文件
//
//go:embed application-dev.yml
var devConfig string

//go:embed application-prod.yml
var prodConfig string

//go:embed application-linux.yml
var linuxConfig string

func init() {
	loadConfig()
}

func loadConfig() {
	// 获取可执行文件所在目录
	execPath, err := os.Executable()
	if err != nil {
		log.Fatalf("Failed to get executable path: %v", err)
	}
	execDir := filepath.Dir(execPath)

	// 获取环境变量，默认为dev
	env := os.Getenv("GO_ENV")
	if env == "" {
		env = "dev"
	}

	// 设置环境变量前缀
	viper.SetEnvPrefix("MOZHAO")
	viper.AutomaticEnv()

	// 设置环境变量键名映射
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// 1. 直接加载环境特定配置
	loadEnvironmentConfig(env, execDir)

	// 2. 尝试加载外部配置文件（会覆盖嵌入的配置）
	loadExternalConfig(execDir)

	if err := viper.Unmarshal(&ConfigData); err != nil {
		log.Fatalf("Unable to decode into struct, %v", err)
	}

	log.Printf("✅ Configuration loaded successfully for environment: %s", env)
	log.Printf("📁 Executable directory: %s", execDir)
}

// loadExternalConfig 尝试加载外部配置文件
func loadExternalConfig(execDir string) {
	// 检查可执行文件同级目录
	externalConfigPath := filepath.Join(execDir, "application.yml")
	if _, err := os.Stat(externalConfigPath); err == nil {
		viper.SetConfigFile(externalConfigPath)
		if err := viper.MergeInConfig(); err != nil {
			log.Printf("⚠️  Warning: Could not merge external config %s: %v", externalConfigPath, err)
		} else {
			log.Printf("📄 Loaded external config: %s", externalConfigPath)
			return
		}
	}

	// 检查当前工作目录
	if _, err := os.Stat("application.yml"); err == nil {
		viper.SetConfigFile("application.yml")
		if err := viper.MergeInConfig(); err != nil {
			log.Printf("⚠️  Warning: Could not merge external config application.yml: %v", err)
		} else {
			log.Printf("📄 Loaded external config: application.yml")
			return
		}
	}

	log.Println("📦 No external config found, using embedded configuration")
}

// loadEnvironmentConfig 加载环境特定配置
func loadEnvironmentConfig(env, execDir string) {
	var envConfigContent string

	// 设置配置类型
	viper.SetConfigType("yml")

	// 根据环境选择嵌入的配置
	switch env {
	case "dev":
		envConfigContent = devConfig
	case "prod":
		envConfigContent = prodConfig
	case "linux":
		envConfigContent = linuxConfig
	default:
		log.Printf("⚠️  Unknown environment: %s, using dev config", env)
		envConfigContent = devConfig
	}

	// 先加载嵌入的环境配置
	if envConfigContent != "" {
		if err := viper.ReadConfig(strings.NewReader(envConfigContent)); err != nil {
			log.Fatalf("Failed to load embedded %s config: %v", env, err)
		} else {
			log.Printf("📦 Loaded embedded %s configuration", env)
		}
	}

	// 尝试加载外部环境配置文件（会覆盖嵌入的环境配置）
	envConfigFileName := fmt.Sprintf("application-%s.yml", env)

	// 检查可执行文件同级目录
	externalEnvConfigPath := filepath.Join(execDir, envConfigFileName)
	if _, err := os.Stat(externalEnvConfigPath); err == nil {
		viper.SetConfigFile(externalEnvConfigPath)
		if err := viper.MergeInConfig(); err != nil {
			log.Printf("⚠️  Warning: Could not merge external %s config %s: %v", env, externalEnvConfigPath, err)
		} else {
			log.Printf("📄 Loaded external %s config: %s", env, externalEnvConfigPath)
		}
		return
	}

	// 检查当前工作目录
	if _, err := os.Stat(envConfigFileName); err == nil {
		viper.SetConfigFile(envConfigFileName)
		if err := viper.MergeInConfig(); err != nil {
			log.Printf("⚠️  Warning: Could not merge external %s config %s: %v", env, envConfigFileName, err)
		} else {
			log.Printf("📄 Loaded external %s config: %s", env, envConfigFileName)
		}
	}
}
