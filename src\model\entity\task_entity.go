package entity

// TaskEntity 任务表 - 存储系统预定义的任务类型，不提供增删改接口
type TaskEntity struct {
	BaseEntity
	TaskCode        string `json:"taskCode" gorm:"column:task_code;type:varchar(50);not null;unique;comment:任务代码"`
	TaskName        string `json:"taskName" gorm:"column:task_name;type:varchar(100);not null;comment:任务名称"`
	TaskDescription string `json:"taskDescription" gorm:"column:task_description;type:text;comment:任务描述"`
	TaskType        string `json:"taskType" gorm:"column:task_type;type:varchar(20);not null;comment:任务类型(SYSTEM,CUSTOM)"`
	Category        string `json:"category" gorm:"column:category;type:varchar(50);comment:任务分类"`
	DefaultConfig   string `json:"defaultConfig" gorm:"column:default_config;type:json;comment:默认配置参数"`
	Status          int    `json:"status" gorm:"column:status;type:tinyint;default:1;comment:状态(0:禁用,1:启用)"`
}

func (TaskEntity) TableName() string {
	return "task"
}
