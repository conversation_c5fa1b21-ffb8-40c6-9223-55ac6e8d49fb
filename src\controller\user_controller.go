package controller

import (
	"mozhao/src/service"
	userService "mozhao/src/service/user"

	"github.com/gin-gonic/gin"
)

func UserController(rootGroup *gin.RouterGroup) {
	// 公开接口 - 不需要权限检查
	publicGroup := rootGroup.Group("/user")
	publicGroup.POST("/getPublicKey", getPublicKey)
	publicGroup.POST("/login", login)
	publicGroup.POST("/register", register)

	// 需要权限检查的接口
	userGroup := rootGroup.Group("/user", service.PrivilegeCheck)
	userGroup.POST("/addPlatform", service.LoginCheck, addPlatform)
}

// login 登录
// @Summary      登录
// @Description  通过加密账号密码登录
// @Tags         user
// @Accept       json
// @Produce      json
// @Param        body body user.userSecret true "加密的用户登录信息"
// @Success      200  {object}  utils.Result{data=user.LoginVo} "登录成功返回用户信息和token"
// @Router       /api/user/login [post]
func login(context *gin.Context) {
	Handler(context, userService.Login)
}

// register 注册
// @Summary      注册
// @Description  通过加密账号密码注册
// @Tags         user
// @Accept       json
// @Produce      json
// @Param        body body user.userSecret true "加密的用户注册信息"
// @Success      200  {object}  utils.Result{data=bool} "注册结果"
// @Router       /api/user/register [post]
func register(context *gin.Context) {
	Handler(context, userService.Register)
}

// getPublicKey 获取公钥
// @Summary      获取RSA公钥
// @Description  获取用于加密的RSA公钥
// @Tags         user
// @Accept       json
// @Produce      json
// @Success      200  {object}  utils.Result{data=user.rsaPublic} "RSA公钥信息"
// @Router       /api/user/getPublicKey [post]
func getPublicKey(context *gin.Context) {
	Handler(context, userService.GetPublicKey)
}

// addPlatform 添加平台
// @Summary      添加平台账号
// @Description  为用户添加第三方平台账号信息
// @Tags         user
// @Accept       json
// @Produce      json
// @Param        body body user.userPlatformSecret true "加密的平台账号信息"
// @Success      200  {object}  utils.Result{data=bool} "添加结果"
// @Security     Bearer
// @Router       /api/user/addPlatform [post]
func addPlatform(context *gin.Context) {
	Handler(context, userService.AddPlatform)
}
