package controller

import (
	"mozhao/src/service"
	"mozhao/src/service/data_collection"

	"github.com/gin-gonic/gin"
)

// DataCollectionController 数据采集控制器
func DataCollectionController(rootGroup *gin.RouterGroup) {
	dataCollectionGroup := rootGroup.Group("/data-collection", service.FullCheck)
	dataCollectionGroup.POST("/profile", addProfileData)
	dataCollectionGroup.POST("/post", addPostData)
	dataCollectionGroup.POST("/contact", addContactData)
	dataCollectionGroup.POST("/search-result", addSearchResultData)
	dataCollectionGroup.POST("/group", addGroupData)
}

// addProfileData 添加用户资料数据
// @Summary	添加用户资料数据
// @Description 存储用户资料数据到数据库
// @Tags	data-collection
// @Accept	json
// @Produce	json
// @Param	body body data_collection.AddProfileDataRequest true "用户资料数据"
// @Success	200 {object} utils.Result{data=bool} "添加结果"
// @Security	Bearer
// @Router	/api/data-collection/profile [post]
func addProfileData(ctx *gin.Context) {
	Handler(ctx, data_collection.AddProfileData)
}

// addPostData 添加帖子数据
// @Summary	添加帖子数据
// @Description 存储帖子内容数据到数据库
// @Tags	data-collection
// @Accept	json
// @Produce	json
// @Param	body body data_collection.AddPostDataRequest true "帖子数据"
// @Success	200 {object} utils.Result{data=bool} "添加结果"
// @Security	Bearer
// @Router	/api/data-collection/post [post]
func addPostData(ctx *gin.Context) {
	Handler(ctx, data_collection.AddPostData)
}

// addContactData 添加联系人数据
// @Summary	添加联系人数据
// @Description 存储联系人数据到数据库
// @Tags	data-collection
// @Accept	json
// @Produce	json
// @Param	body body data_collection.AddContactDataRequest true "联系人数据"
// @Success	200 {object} utils.Result{data=bool} "添加结果"
// @Security	Bearer
// @Router	/api/data-collection/contact [post]
func addContactData(ctx *gin.Context) {
	Handler(ctx, data_collection.AddContactData)
}

// addSearchResultData 添加搜索结果数据
// @Summary	添加搜索结果数据
// @Description 存储搜索结果数据到数据库
// @Tags	data-collection
// @Accept	json
// @Produce	json
// @Param	body body data_collection.AddSearchResultDataRequest true "搜索结果数据"
// @Success	200 {object} utils.Result{data=bool} "添加结果"
// @Security	Bearer
// @Router	/api/data-collection/search-result [post]
func addSearchResultData(ctx *gin.Context) {
	Handler(ctx, data_collection.AddSearchResultData)
}

// addGroupData 添加群组数据
// @Summary	添加群组数据
// @Description 存储群组社区数据到数据库
// @Tags	data-collection
// @Accept	json
// @Produce	json
// @Param	body body data_collection.AddGroupDataRequest true "群组数据"
// @Success	200 {object} utils.Result{data=bool} "添加结果"
// @Security	Bearer
// @Router	/api/data-collection/group [post]
func addGroupData(ctx *gin.Context) {
	Handler(ctx, data_collection.AddGroupData)
}
