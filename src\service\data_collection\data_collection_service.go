package data_collection

import (
	"encoding/json"
	"errors"
	"mozhao/src/common/consts"
	"mozhao/src/common/mysql"
	"mozhao/src/common/utils"
	"mozhao/src/model/entity"
	"time"

	"github.com/gin-gonic/gin"
)

// 请求模型定义

// AddProfileDataRequest 添加用户资料数据请求
type AddProfileDataRequest struct {
	ProfileDataID   string    `json:"profileDataId,omitempty"`
	Username        string    `json:"username"`
	DisplayName     string    `json:"displayName"`
	Bio             string    `json:"bio,omitempty"`
	Avatar          string    `json:"avatar,omitempty"`
	FollowerCount   int64     `json:"followerCount,omitempty"`
	FollowingCount  int64     `json:"followingCount,omitempty"`
	PostCount       int64     `json:"postCount,omitempty"`
	Verified        bool      `json:"verified,omitempty"`
	BusinessAccount bool      `json:"businessAccount,omitempty"`
	Email           string    `json:"email,omitempty"`
	Phone           string    `json:"phone,omitempty"`
	Website         string    `json:"website,omitempty"`
	Country         string    `json:"country,omitempty"`
	City            string    `json:"city,omitempty"`
	Workplace       string    `json:"workplace,omitempty"`
	Position        string    `json:"position,omitempty"`
	Education       string    `json:"education,omitempty"`
	ProfileUrl      string    `json:"profileUrl"`
	Platform        string    `json:"platform"`
	CollectedAt     time.Time `json:"collectedAt"`
}

// AddPostDataRequest 添加帖子数据请求
type AddPostDataRequest struct {
	PostDataID   string    `json:"postDataId,omitempty"`
	AuthorID     string    `json:"authorId"`
	AuthorName   string    `json:"authorName"`
	Content      string    `json:"content"`
	MediaUrls    []string  `json:"mediaUrls,omitempty"`
	MediaTypes   []string  `json:"mediaTypes,omitempty"`
	LikeCount    int64     `json:"likeCount,omitempty"`
	CommentCount int64     `json:"commentCount,omitempty"`
	ShareCount   int64     `json:"shareCount,omitempty"`
	ViewCount    int64     `json:"viewCount,omitempty"`
	PublishedAt  time.Time `json:"publishedAt"`
	Timestamp    string    `json:"timestamp"`
	Hashtags     []string  `json:"hashtags,omitempty"`
	Mentions     []string  `json:"mentions,omitempty"`
	PostUrl      string    `json:"postUrl,omitempty"`
	Platform     string    `json:"platform"`
	CollectedAt  time.Time `json:"collectedAt"`
	IsAd         bool      `json:"isAd,omitempty"`
}

// AddContactDataRequest 添加联系人数据请求
type AddContactDataRequest struct {
	ContactDataID string     `json:"contactDataId,omitempty"`
	Name          string     `json:"name"`
	Username      string     `json:"username,omitempty"`
	Email         string     `json:"email,omitempty"`
	Phone         string     `json:"phone,omitempty"`
	Company       string     `json:"company,omitempty"`
	Position      string     `json:"position,omitempty"`
	Facebook      string     `json:"facebook,omitempty"`
	Instagram     string     `json:"instagram,omitempty"`
	Twitter       string     `json:"twitter,omitempty"`
	LinkedIn      string     `json:"linkedin,omitempty"`
	Tags          []string   `json:"tags,omitempty"`
	Source        string     `json:"source"`
	Notes         string     `json:"notes,omitempty"`
	CollectedAt   time.Time  `json:"collectedAt"`
	LastContact   *time.Time `json:"lastContact,omitempty"`
}

// AddSearchResultDataRequest 添加搜索结果数据请求
type AddSearchResultDataRequest struct {
	SearchResultDataID string    `json:"searchResultDataId,omitempty"`
	Name               string    `json:"name"`
	Username           string    `json:"username,omitempty"`
	ProfileUrl         string    `json:"profileUrl"`
	Avatar             string    `json:"avatar,omitempty"`
	Description        string    `json:"description,omitempty"`
	Snippet            string    `json:"snippet,omitempty"`
	FollowerCount      int64     `json:"followerCount,omitempty"`
	Keyword            string    `json:"keyword"`
	Platform           string    `json:"platform"`
	Rank               int       `json:"rank,omitempty"`
	CollectedAt        time.Time `json:"collectedAt"`
}

// AddGroupDataRequest 添加群组数据请求
type AddGroupDataRequest struct {
	GroupDataID string    `json:"groupDataId,omitempty"`
	Name        string    `json:"name"`
	Description string    `json:"description,omitempty"`
	MemberCount int64     `json:"memberCount,omitempty"`
	IsPrivate   bool      `json:"isPrivate,omitempty"`
	Category    string    `json:"category,omitempty"`
	AdminIds    []string  `json:"adminIds,omitempty"`
	Rules       []string  `json:"rules,omitempty"`
	GroupUrl    string    `json:"groupUrl,omitempty"`
	Platform    string    `json:"platform"`
	CollectedAt time.Time `json:"collectedAt"`
}

// 辅助函数：将字符串数组转换为JSON字符串
func sliceToJSON(slice []string) string {
	if len(slice) == 0 {
		return ""
	}
	jsonBytes, err := json.Marshal(slice)
	if err != nil {
		return ""
	}
	return string(jsonBytes)
}

// AddProfileData 添加用户资料数据
func AddProfileData(ctx *gin.Context) (any, error) {
	var request AddProfileDataRequest
	if err := ctx.ShouldBind(&request); err != nil {
		return false, errors.New(consts.ParamError)
	}

	userId := utils.GetUser(ctx)
	profileId := utils.GenerateSnowFlakeID()

	profileEntity := entity.ProfileDataEntity{
		BaseEntity: entity.BaseEntity{
			Id: profileId,
		},
		UserId:          userId,
		ProfileDataID:   request.ProfileDataID,
		Username:        request.Username,
		DisplayName:     request.DisplayName,
		Bio:             request.Bio,
		Avatar:          request.Avatar,
		FollowerCount:   request.FollowerCount,
		FollowingCount:  request.FollowingCount,
		PostCount:       request.PostCount,
		Verified:        request.Verified,
		BusinessAccount: request.BusinessAccount,
		Email:           request.Email,
		Phone:           request.Phone,
		Website:         request.Website,
		Country:         request.Country,
		City:            request.City,
		Workplace:       request.Workplace,
		Position:        request.Position,
		Education:       request.Education,
		ProfileUrl:      request.ProfileUrl,
		Platform:        request.Platform,
		CollectedAt:     request.CollectedAt,
	}

	if err := mysql.Db.Create(&profileEntity).Error; err != nil {
		return false, errors.New("数据保存失败")
	}

	return true, nil
}

// AddPostData 添加帖子数据
func AddPostData(ctx *gin.Context) (any, error) {
	var request AddPostDataRequest
	if err := ctx.ShouldBind(&request); err != nil {
		return false, errors.New(consts.ParamError)
	}

	userId := utils.GetUser(ctx)
	postId := utils.GenerateSnowFlakeID()

	postEntity := entity.PostDataEntity{
		BaseEntity: entity.BaseEntity{
			Id: postId,
		},
		UserId:       userId,
		PostDataID:   request.PostDataID,
		AuthorID:     request.AuthorID,
		AuthorName:   request.AuthorName,
		Content:      request.Content,
		MediaUrls:    sliceToJSON(request.MediaUrls),
		MediaTypes:   sliceToJSON(request.MediaTypes),
		LikeCount:    request.LikeCount,
		CommentCount: request.CommentCount,
		ShareCount:   request.ShareCount,
		ViewCount:    request.ViewCount,
		PublishedAt:  request.PublishedAt,
		Timestamp:    request.Timestamp,
		Hashtags:     sliceToJSON(request.Hashtags),
		Mentions:     sliceToJSON(request.Mentions),
		PostUrl:      request.PostUrl,
		Platform:     request.Platform,
		CollectedAt:  request.CollectedAt,
		IsAd:         request.IsAd,
	}

	if err := mysql.Db.Create(&postEntity).Error; err != nil {
		return false, errors.New("数据保存失败")
	}

	return true, nil
}

// AddContactData 添加联系人数据
func AddContactData(ctx *gin.Context) (any, error) {
	var request AddContactDataRequest
	if err := ctx.ShouldBind(&request); err != nil {
		return false, errors.New(consts.ParamError)
	}

	userId := utils.GetUser(ctx)
	contactId := utils.GenerateSnowFlakeID()

	contactEntity := entity.ContactDataEntity{
		BaseEntity: entity.BaseEntity{
			Id: contactId,
		},
		UserId:        userId,
		ContactDataID: request.ContactDataID,
		Name:          request.Name,
		Username:      request.Username,
		Email:         request.Email,
		Phone:         request.Phone,
		Company:       request.Company,
		Position:      request.Position,
		Facebook:      request.Facebook,
		Instagram:     request.Instagram,
		Twitter:       request.Twitter,
		LinkedIn:      request.LinkedIn,
		Tags:          sliceToJSON(request.Tags),
		Source:        request.Source,
		Notes:         request.Notes,
		CollectedAt:   request.CollectedAt,
		LastContact:   request.LastContact,
	}

	if err := mysql.Db.Create(&contactEntity).Error; err != nil {
		return false, errors.New("数据保存失败")
	}

	return true, nil
}

// AddSearchResultData 添加搜索结果数据
func AddSearchResultData(ctx *gin.Context) (any, error) {
	var request AddSearchResultDataRequest
	if err := ctx.ShouldBind(&request); err != nil {
		return false, errors.New(consts.ParamError)
	}

	userId := utils.GetUser(ctx)
	searchResultId := utils.GenerateSnowFlakeID()

	searchResultEntity := entity.SearchResultDataEntity{
		BaseEntity: entity.BaseEntity{
			Id: searchResultId,
		},
		UserId:             userId,
		SearchResultDataID: request.SearchResultDataID,
		Name:               request.Name,
		Username:           request.Username,
		ProfileUrl:         request.ProfileUrl,
		Avatar:             request.Avatar,
		Description:        request.Description,
		Snippet:            request.Snippet,
		FollowerCount:      request.FollowerCount,
		Keyword:            request.Keyword,
		Platform:           request.Platform,
		Rank:               request.Rank,
		CollectedAt:        request.CollectedAt,
	}

	if err := mysql.Db.Create(&searchResultEntity).Error; err != nil {
		return false, errors.New("数据保存失败")
	}

	return true, nil
}

// AddGroupData 添加群组数据
func AddGroupData(ctx *gin.Context) (any, error) {
	var request AddGroupDataRequest
	if err := ctx.ShouldBind(&request); err != nil {
		return false, errors.New(consts.ParamError)
	}

	userId := utils.GetUser(ctx)
	groupId := utils.GenerateSnowFlakeID()

	groupEntity := entity.GroupDataEntity{
		BaseEntity: entity.BaseEntity{
			Id: groupId,
		},
		UserId:      userId,
		GroupDataID: request.GroupDataID,
		Name:        request.Name,
		Description: request.Description,
		MemberCount: request.MemberCount,
		IsPrivate:   request.IsPrivate,
		Category:    request.Category,
		AdminIds:    sliceToJSON(request.AdminIds),
		Rules:       sliceToJSON(request.Rules),
		GroupUrl:    request.GroupUrl,
		Platform:    request.Platform,
		CollectedAt: request.CollectedAt,
	}

	if err := mysql.Db.Create(&groupEntity).Error; err != nil {
		return false, errors.New("数据保存失败")
	}

	return true, nil
}
