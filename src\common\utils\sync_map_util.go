package utils

import "sync"

// TypedSyncMap 线程安全且类型明确的同步映射
type TypedSyncMap[K comparable, V any] struct {
	m sync.Map
}

func CreateTypedSyncMap[K comparable, V any]() *TypedSyncMap[K, V] {
	return &TypedSyncMap[K, V]{}
}

// Store 安全存储键值对
func (tsm *TypedSyncMap[K, V]) Store(key K, value V) {
	tsm.m.Store(key, value)
}

// Load 获取键对应值（带类型转换）
func (tsm *TypedSyncMap[K, V]) Load(key K) (V, bool) {
	val, ok := tsm.m.Load(key)
	if !ok {
		var zero V
		return zero, false
	}
	return val.(V), true
}

// LoadOrStore 存在则加载，不存在则存储
func (tsm *TypedSyncMap[K, V]) LoadOrStore(key K, value V) (V, bool) {
	actual, loaded := tsm.m.LoadOrStore(key, value)
	return actual.(V), loaded
}

// Delete 删除指定键
func (tsm *TypedSyncMap[K, V]) Delete(key K) {
	tsm.m.Delete(key)
}

// Range 安全遍历映射（自动类型转换）
func (tsm *TypedSyncMap[K, V]) Range(f func(key K, value V) bool) {
	tsm.m.Range(func(k, v interface{}) bool {
		return f(k.(K), v.(V))
	})
}

func (tsm *TypedSyncMap[K, V]) ComputeIfAbsent(key K, fn func() V) V {
	if v, ok := tsm.Load(key); ok {
		return v
	}

	newVal := fn()
	actual, loaded := tsm.LoadOrStore(key, newVal)
	if loaded {
		return actual
	}
	return newVal
}

// GetOrDefault 安全获取或返回默认值
func (tsm *TypedSyncMap[K, V]) GetOrDefault(key K, defaultValue V) V {
	if v, ok := tsm.Load(key); ok {
		return v
	}
	return defaultValue
}

func (tsm *TypedSyncMap[K, V]) StoreAll(pairs map[K]V) {
	for k, v := range pairs {
		tsm.Store(k, v)
	}
}

// LoadAll 批量加载
func (tsm *TypedSyncMap[K, V]) LoadAll(keys []K) map[K]V {
	result := make(map[K]V)
	for _, key := range keys {
		if val, ok := tsm.Load(key); ok {
			result[key] = val
		}
	}
	return result
}
