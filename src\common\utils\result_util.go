package utils

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

type Result struct {
	Code int    `json:"code" example:"200"`
	Msg  string `json:"msg" example:"success"`
	Data any    `json:"data"`
}

func Success(data any, context *gin.Context, msg ...string) {
	var message string
	if len(msg) > 0 {
		message = msg[0]
	} else {
		message = "success"
	}
	result := Result{
		Data: data,
		Code: http.StatusOK,
		Msg:  message,
	}
	context.AbortWithStatusJSON(http.StatusOK, result)
}

func Err(err string, context *gin.Context) {
	result := Result{
		Code: http.StatusOK,
		Msg:  err,
	}
	context.AbortWithStatusJSON(http.StatusBadRequest, result)
}
