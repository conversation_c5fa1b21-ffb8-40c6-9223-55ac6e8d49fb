package controller

import (
	aiService "mozhao/src/service/ai"

	"github.com/gin-gonic/gin"
)

func AiController(rootGroup *gin.RouterGroup) {
	aiGroup := rootGroup.Group("/ai")
	aiGroup.POST("/textMessage", textMessage)
	aiGroup.POST("/captchaRecognition", captchaRecognition)
	aiGroup.POST("/identify", identify)
}

// textMessage 文本对话
// @Summary	AI文本对话
// @Description 与AI进行多轮文本对话
// @Tags	ai
// @Accept	json
// @Produce	json
// @Param	body body ai.Contents true "对话内容列表"
// @Success	200 {object} utils.Result{data=string} "AI回复内容"
// @Router	/api/ai/textMessage [post]
func textMessage(context *gin.Context) {
	Handler(context, aiService.TextMessage)
}

// captchaRecognition 验证码识别
// @Summary	验证码识别
// @Description 使用AI识别验证码图片内容
// @Tags	ai
// @Accept	json
// @Produce	json
// @Param	body body ai.Content true "验证码图片内容"
// @Success	200 {object} utils.Result{data=string} "识别结果"
// @Router	/api/ai/captchaRecognition [post]
func captchaRecognition(context *gin.Context) {
	Handler(context, aiService.CaptchaRecognition)
}

// identify 音频识别
// @Summary	音频识别
// @Description 识别音频文件内容
// @Tags	ai
// @Accept	json
// @Produce	json
// @Param	body body ai.TranscriptionRequest true "音频识别请求"
// @Success	200 {object} utils.Result{data=string} "识别结果"
// @Router	/api/ai/identify [post]
func identify(context *gin.Context) {
	Handler(context, aiService.Identify)
}
