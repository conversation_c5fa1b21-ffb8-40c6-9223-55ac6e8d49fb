package rdb

import (
	"context"
	"fmt"
	"mozhao/src/config"

	"github.com/redis/go-redis/v9"
)

var RedisCtx = context.Background()

var RedisClient *redis.Client

func init() {
	configData := config.ConfigData
	rdb := redis.NewClient(&redis.Options{
		Addr:     configData.Redis.Addr, // Redis 地址
		Password: configData.Redis.Password,
		DB:       configData.Redis.Db, // 密码（没有则留空）
	})

	// 测试连接
	pong, err := rdb.Ping(RedisCtx).Result()
	if err != nil {
		panic(err)
	}
	fmt.Println("连接成功:", pong)
	RedisClient = rdb
}
