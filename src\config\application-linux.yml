# 生产环境配置文件
server:
  path: "/api"
  port: ":8080"

database:
  host: *************
  port: 3306
  userName: artorias
  password: Artorias!741
  database: mozhao
redis:
  addr: *************:6379
  password: artorias
  db: 0
jwt:
  key: unevenckingartoriasheavenkillworldunevenckingartoriasheavenkillworldunevenckingartoriasheavenkillworld
ai:
  deepSeekR1DistillQwen7B:
    url: https://api.siliconflow.cn/v1
    model: deepseek-ai/DeepSeek-R1-Distill-Qwen-7B
    authorization: sk-lwikybvteyqbwwfnybhqehhsdebzpykumglseyvoofjrnmbg
  deepseekAiDeepseekVl2:
    url: https://api.siliconflow.cn/v1
    model: deepseek-ai/deepseek-vl2
    authorization: sk-vhlfdnhwxauhamfyhfmlbezdnzxhjvapnmihlfoanghkekry
  proQwenQwen2VL7BInstruct:
    url: https://api.siliconflow.cn/v1
    model: deepseek-ai/deepseek-vl2
    authorization: sk-vhlfdnhwxauhamfyhfmlbezdnzxhjvapnmihlfoanghkekry
  qwenVlMax:
    url: https://dashscope.aliyuncs.com/compatible-mode/v1
    model: qwen-vl-max
    authorization: sk-55d08303d06146fbaf0bfa9c2338ab59
  deepseekReasoner:
    url: https://api.deepseek.com
    model: deepseek-reasoner
    authorization: ***********************************
  deepseekChat:
    url: https://api.deepseek.com
    model: deepseek-chat
    authorization: ***********************************

audioIdentify:
  transcriptionUrl: https://dashscope.aliyuncs.com/api/v1/services/audio/asr/transcription
  taskUrl: https://dashscope.aliyuncs.com/api/v1/tasks/%s
  authorization: sk-55d08303d06146fbaf0bfa9c2338ab59
  model: paraformer-v2